import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from src.cnn_model import SurfEmbCNN
from dataset import SyntheticPoseDataset
import numpy as np
import os

def train_model(model, dataloader, optimizer, criterion, num_epochs, device, save_path="models/surfemb_model.pth"):
    """
    Trains the SurfEmbCNN model.

    Args:
        model (nn.Module): The SurfEmbCNN model to train.
        dataloader (DataLoader): DataLoader for the training dataset.
        optimizer (optim.Optimizer): Optimizer for model parameters.
        criterion (nn.Module): Loss function (e.g., MSELoss).
        num_epochs (int): Number of training epochs.
        device (torch.device): Device to train on (cpu or cuda).
        save_path (str): Path to save the trained model.
    """
    model.train()
    model.to(device)

    if not os.path.exists(os.path.dirname(save_path)):
        os.makedirs(os.path.dirname(save_path))

    for epoch in range(num_epochs):
        running_loss = 0.0
        for i, (rgbd_input, object_mask, gt_correspondence_map) in enumerate(dataloader):
            rgbd_input = rgbd_input.to(device)
            object_mask = object_mask.to(device)
            gt_correspondence_map = gt_correspondence_map.to(device)

            optimizer.zero_grad()

            # Forward pass
            predicted_correspondence_map = model(rgbd_input)

            # Apply mask to both ground truth and predicted maps
            # The mask is 1-channel, expand to 3 channels for element-wise multiplication
            masked_gt_correspondence_map = gt_correspondence_map * object_mask
            masked_predicted_correspondence_map = predicted_correspondence_map * object_mask

            # Calculate loss only on masked regions
            loss = criterion(masked_predicted_correspondence_map, masked_gt_correspondence_map)

            # Backward and optimize
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

            if i % 10 == 0: # Print every 10 mini-batches
                print(f"Epoch [{epoch+1}/{num_epochs}], Step [{i+1}/{len(dataloader)}], Loss: {loss.item():.4f}")

        epoch_loss = running_loss / len(dataloader)
        print(f"Epoch [{epoch+1}/{num_epochs}] finished, Average Loss: {epoch_loss:.4f}")

        # Save model checkpoint
        torch.save(model.state_dict(), save_path.replace(".pth", f"_epoch_{epoch+1}.pth"))
        print(f"Model saved to {save_path.replace(".pth", f"_epoch_{epoch+1}.pth")}")

    print("Training complete.")
    torch.save(model.state_dict(), save_path)
    print(f"Final model saved to {save_path}")

# Example usage (for testing purposes)
if __name__ == "__main__":
    # Create a dummy PLY file for testing
    if not os.path.exists("data"): os.makedirs("data")
    dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
    dummy_mesh.compute_vertex_normals()
    o3d.io.write_triangle_mesh("data/dummy_model.ply", dummy_mesh)

    # Create a dummy render_option.json for Open3D
    if not os.path.exists("utils"): os.makedirs("utils")
    with open("utils/render_option.json", "w") as f:
        f.write("{\"background_color\": [0, 0, 0], \"point_size\": 1.0, \"line_width\": 1.0}")

    camera_intrinsics = {
        'fx': 615.0, 'fy': 615.0, 'cx': 320.0, 'cy': 240.0
    }
    image_size = (240, 320) # H, W

    # Hyperparameters
    num_epochs = 2
    batch_size = 2
    learning_rate = 0.001

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Dataset and DataLoader
    print("Creating synthetic dataset...")
    dataset = SyntheticPoseDataset(
        cad_model_path="data/dummy_model.ply",
        camera_intrinsics=camera_intrinsics,
        image_size=image_size,
        num_samples=batch_size * 5 # Small number for quick test
    )
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    # Model, Optimizer, Loss
    print("Initializing model...")
    model = SurfEmbCNN(in_channels=4, out_channels=3, backbone="resnet18", pretrained=False)
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.MSELoss() # Mean Squared Error Loss for regression of 3D coordinates

    # Train the model
    print("Starting training...")
    train_model(model, dataloader, optimizer, criterion, num_epochs, device, save_path="models/test_surfemb_model.pth")

    print("Training script test finished.")

    # Clean up dummy files
    os.remove("data/dummy_model.ply")
    os.remove("utils/render_option.json")
    os.rmdir("data")
    os.rmdir("utils")
    # Remove generated model checkpoints
    for epoch in range(num_epochs):
        if os.path.exists(f"models/test_surfemb_model_epoch_{epoch+1}.pth"):
            os.remove(f"models/test_surfemb_model_epoch_{epoch+1}.pth")
    if os.path.exists("models/test_surfemb_model.pth"):
        os.remove("models/test_surfemb_model.pth")
    os.rmdir("models")


