import torch
import torch.nn as nn
import torchvision.models as models

class SurfEmbCNN(nn.Module):
    """
    Convolutional Neural Network for Surface Embedding-based 6D Pose Estimation.
    This network takes an RGBD image as input and outputs a correspondence map,
    where each pixel corresponding to the object predicts its 3D coordinate
    on the object's CAD model.
    """
    def __init__(self, in_channels=4, out_channels=3, backbone="resnet18", pretrained=True):
        """
        Initializes the SurfEmbCNN model.
        Args:
            in_channels (int): Number of input channels (e.g., 3 for RGB, 4 for RGBD).
            out_channels (int): Number of output channels (3 for x, y, z coordinates).
            backbone (str): Name of the backbone architecture (e.g., "resnet18", "resnet34").
            pretrained (bool): Whether to use a pretrained backbone.
        """
        super(SurfEmbCNN, self).__init__()

        if backbone == "resnet18":
            self.encoder = models.resnet18(pretrained=pretrained)
        elif backbone == "resnet34":
            self.encoder = models.resnet34(pretrained=pretrained)
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")

        # Modify the first convolutional layer to accept `in_channels`
        # Original ResNet expects 3 input channels. If in_channels != 3, we need to adapt.
        if in_channels != 3:
            self.encoder.conv1 = nn.Conv2d(
                in_channels,
                self.encoder.conv1.out_channels,
                kernel_size=self.encoder.conv1.kernel_size,
                stride=self.encoder.conv1.stride,
                padding=self.encoder.conv1.padding,
                bias=False
            )
            # Initialize the new conv1 layer. For RGBD, we can copy RGB weights and add a new channel.
            if pretrained:
                # A simple way: average the weights of the first 3 channels for the new channel
                # Or, for depth, initialize with zeros or small random values
                original_weights = self.encoder.conv1.weight.data
                new_weights = torch.zeros(
                    original_weights.shape[0],
                    in_channels,
                    original_weights.shape[2],
                    original_weights.shape[3]
                )
                # Copy RGB weights (first 3 channels)
                min_channels = min(3, original_weights.shape[1])
                new_weights[:, :min_channels, :, :] = original_weights[:, :min_channels, :, :]
                # For the depth channel (index 3), we can initialize with zeros or a small average
                # For simplicity, let's just copy the average of RGB channels for the 4th channel
                if in_channels == 4 and min_channels == 3:
                    new_weights[:, 3, :, :] = torch.mean(original_weights[:, :3, :, :], dim=1)
                self.encoder.conv1.weight = nn.Parameter(new_weights)

        # Remove the fully connected layer at the end of the encoder
        self.encoder = nn.Sequential(*list(self.encoder.children())[:-2])

        # Decoder part (simple upsampling and convolution for dense prediction)
        # The output of ResNet-18/34 encoder is typically 512 channels (for the last block)
        self.decoder = nn.Sequential(
            nn.ConvTranspose2d(512, 256, kernel_size=4, stride=2, padding=1),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(256, 128, kernel_size=4, stride=2, padding=1),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(128, 64, kernel_size=4, stride=2, padding=1),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(64, 32, kernel_size=4, stride=2, padding=1),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(32, out_channels, kernel_size=4, stride=2, padding=1),
            # No activation here, as we are predicting continuous 3D coordinates
        )

    def forward(self, x):
        """
        Forward pass through the network.
        Args:
            x (torch.Tensor): Input tensor of shape (B, in_channels, H, W).
        Returns:
            torch.Tensor: Output correspondence map of shape (B, out_channels, H, W).
        """
        x = self.encoder(x)
        x = self.decoder(x)
        return x

# Example usage (for testing purposes)
if __name__ == "__main__":
    # Test with RGBD input (4 channels)
    input_tensor_rgbd = torch.randn(1, 4, 256, 256) # Batch size 1, 4 channels, 256x256 image
    model_rgbd = SurfEmbCNN(in_channels=4, backbone="resnet18", pretrained=False)
    output_rgbd = model_rgbd(input_tensor_rgbd)
    print(f"Output shape for RGBD input: {output_rgbd.shape}")
    assert output_rgbd.shape == (1, 3, 256, 256), "RGBD output shape mismatch!"

    # Test with RGB input (3 channels)
    input_tensor_rgb = torch.randn(1, 3, 256, 256) # Batch size 1, 3 channels, 256x256 image
    model_rgb = SurfEmbCNN(in_channels=3, backbone="resnet18", pretrained=False)
    output_rgb = model_rgb(input_tensor_rgb)
    print(f"Output shape for RGB input: {output_rgb.shape}")
    assert output_rgb.shape == (1, 3, 256, 256), "RGB output shape mismatch!"

    print("SurfEmbCNN model tests passed!")


