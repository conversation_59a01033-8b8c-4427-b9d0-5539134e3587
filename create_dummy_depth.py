#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create dummy depth images from RGB images for testing purposes.
Since the system requires both RGB and depth images, this script generates
synthetic depth images with reasonable depth values.
"""

import cv2
import numpy as np
import os
import glob

def create_dummy_depth_from_rgb(rgb_path, depth_path, depth_range=(500, 2000)):
    """
    Creates a dummy depth image from an RGB image.
    
    Args:
        rgb_path (str): Path to the RGB image
        depth_path (str): Path where to save the depth image
        depth_range (tuple): Min and max depth values in mm
    """
    # Load RGB image
    rgb = cv2.imread(rgb_path)
    if rgb is None:
        print(f"Could not load RGB image: {rgb_path}")
        return False
    
    # Convert to grayscale for depth generation
    gray = cv2.cvtColor(rgb, cv2.COLOR_BGR2GRAY)
    
    # Create depth based on intensity (darker = closer, lighter = farther)
    # Invert grayscale so darker areas have smaller depth values
    inverted_gray = 255 - gray
    
    # Normalize to depth range
    min_depth, max_depth = depth_range
    depth = (inverted_gray.astype(np.float32) / 255.0) * (max_depth - min_depth) + min_depth
    
    # Add some noise for realism
    noise = np.random.normal(0, 50, depth.shape)
    depth = depth + noise
    
    # Clip to valid range
    depth = np.clip(depth, min_depth, max_depth)
    
    # Convert to uint16 (typical depth image format)
    depth_uint16 = depth.astype(np.uint16)
    
    # Save depth image
    cv2.imwrite(depth_path, depth_uint16)
    print(f"Created depth image: {depth_path}")
    return True

def main():
    # Create depth directory
    rgb_dir = "data/test_rgb"
    depth_dir = "data/test_depth"
    
    if not os.path.exists(depth_dir):
        os.makedirs(depth_dir)
        print(f"Created directory: {depth_dir}")
    
    # Find all RGB images
    rgb_pattern = os.path.join(rgb_dir, "*.jpg")
    rgb_files = glob.glob(rgb_pattern)
    
    if not rgb_files:
        print(f"No RGB images found in {rgb_dir}")
        return
    
    print(f"Found {len(rgb_files)} RGB images")
    
    # Process each RGB image
    for rgb_path in rgb_files:
        # Get filename without extension
        filename = os.path.basename(rgb_path)
        name_without_ext = os.path.splitext(filename)[0]
        
        # Create corresponding depth filename
        depth_filename = f"{name_without_ext}.png"  # Use PNG for depth
        depth_path = os.path.join(depth_dir, depth_filename)
        
        # Create dummy depth image
        create_dummy_depth_from_rgb(rgb_path, depth_path)
    
    print(f"Finished creating {len(rgb_files)} depth images in {depth_dir}")

if __name__ == "__main__":
    main()
