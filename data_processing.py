import open3d as o3d
import numpy as np
import cv2

def load_cad_model(model_path):
    """
    Loads a CAD model from a PLY file.
    Args:
        model_path (str): Path to the PLY model file.
    Returns:
        open3d.geometry.PointCloud: The loaded 3D model as a point cloud.
    """
    mesh = o3d.io.read_triangle_mesh(model_path)
    if not mesh.has_vertices():
        raise ValueError(f"Could not load vertices from {model_path}")
    # Convert mesh to point cloud for easier processing if needed, or keep as mesh
    # For SurfEmb, we need surface coordinates, so a mesh is appropriate.
    return mesh

def process_rgbd_image(rgb_image_path, depth_image_path, camera_intrinsics):
    """
    Loads and processes RGB and depth images.
    Args:
        rgb_image_path (str): Path to the RGB image file.
        depth_image_path (str): Path to the depth image file.
        camera_intrinsics (dict): Dictionary containing camera intrinsic parameters (fx, fy, cx, cy).
    Returns:
        tuple: A tuple containing:
            - numpy.ndarray: Processed RGB image.
            - numpy.ndarray: Processed depth image.
    """
    rgb_image = cv2.imread(rgb_image_path)
    depth_image = cv2.imread(depth_image_path, cv2.IMREAD_UNCHANGED)

    if rgb_image is None:
        raise FileNotFoundError(f"RGB image not found at {rgb_image_path}")
    if depth_image is None:
        raise FileNotFoundError(f"Depth image not found at {depth_image_path}")

    # Convert RGB to a consistent format (e.g., RGB from BGR)
    rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)

    # Depth image is typically 16-bit unsigned integer (uint16) in mm or similar
    # Convert to meters if necessary, or keep original units and adjust camera intrinsics accordingly
    # For D435, depth is usually in millimeters.
    # Example: depth_image = depth_image.astype(np.float32) / 1000.0 # Convert to meters

    return rgb_image, depth_image

def project_3d_to_2d(points_3d, camera_intrinsics):
    """
    Projects 3D points to 2D image coordinates using camera intrinsics.
    Args:
        points_3d (numpy.ndarray): Nx3 array of 3D points.
        camera_intrinsics (dict): Dictionary containing camera intrinsic parameters (fx, fy, cx, cy).
    Returns:
        numpy.ndarray: Nx2 array of 2D projected points.
    """
    fx, fy, cx, cy = camera_intrinsics['fx'], camera_intrinsics['fy'], camera_intrinsics['cx'], camera_intrinsics['cy']
    
    # Ensure points_3d is a numpy array
    points_3d = np.asarray(points_3d)

    # Avoid division by zero for points with z <= 0
    valid_indices = points_3d[:, 2] > 0
    projected_points = np.zeros((points_3d.shape[0], 2))

    if np.any(valid_indices):
        x_proj = fx * points_3d[valid_indices, 0] / points_3d[valid_indices, 2] + cx
        y_proj = fy * points_3d[valid_indices, 1] / points_3d[valid_indices, 2] + cy
        projected_points[valid_indices, 0] = x_proj
        projected_points[valid_indices, 1] = y_proj
    
    # For invalid points, set to a value outside image bounds or handle as needed
    projected_points[~valid_indices] = -1 # Example: mark as invalid

    return projected_points

def unproject_2d_to_3d(u, v, depth, camera_intrinsics):
    """
    Unprojects a 2D pixel to a 3D point using depth and camera intrinsics.
    Args:
        u (int): X-coordinate of the pixel.
        v (int): Y-coordinate of the pixel.
        depth (float): Depth value at the pixel (in meters).
        camera_intrinsics (dict): Dictionary containing camera intrinsic parameters (fx, fy, cx, cy).
    Returns:
        numpy.ndarray: 3D point (x, y, z).
    """
    fx, fy, cx, cy = camera_intrinsics['fx'], camera_intrinsics['fy'], camera_intrinsics['cx'], camera_intrinsics['cy']

    z = depth
    x = (u - cx) * z / fx
    y = (v - cy) * z / fy

    return np.array([x, y, z])


# Example usage (for testing purposes)
if __name__ == "__main__":
    # Create dummy files for testing
    dummy_rgb = np.zeros((480, 640, 3), dtype=np.uint8)
    dummy_depth = np.zeros((480, 640), dtype=np.uint16)
    cv2.imwrite("data/dummy_rgb.png", dummy_rgb)
    cv2.imwrite("data/dummy_depth.png", dummy_depth)

    # Dummy camera intrinsics (example for D435 at 640x480)
    camera_intrinsics = {
        'fx': 615.0, 'fy': 615.0, 'cx': 320.0, 'cy': 240.0
    }

    print("Testing data loading and processing...")
    try:
        rgb, depth = process_rgbd_image("data/dummy_rgb.png", "data/dummy_depth.png", camera_intrinsics)
        print(f"Loaded RGB image shape: {rgb.shape}, Depth image shape: {depth.shape}")
    except Exception as e:
        print(f"Error processing images: {e}")

    print("Testing CAD model loading...")
    # For actual testing, you'd need a real .ply file
    # For now, we'll just simulate a successful load or handle the error.
    try:
        # Create a dummy PLY file for testing
        points = np.array([[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0]], dtype=np.float32)
        colors = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]], dtype=np.float32)
        dummy_mesh = o3d.geometry.TriangleMesh.create_coordinate_frame()
        o3d.io.write_triangle_mesh("data/dummy_model.ply", dummy_mesh)

        model = load_cad_model("data/dummy_model.ply")
        print(f"Loaded CAD model with {len(model.vertices)} vertices.")
    except Exception as e:
        print(f"Error loading CAD model: {e}")

    print("Testing 3D to 2D projection...")
    points_3d_test = np.array([
        [0.1, 0.2, 1.0],  # Point in front of camera
        [-0.5, 0.3, 2.5], # Another point
        [0.0, 0.0, -0.1]  # Point behind camera (should be invalid)
    ])
    projected_2d = project_3d_to_2d(points_3d_test, camera_intrinsics)
    print(f"Projected 2D points:\n{projected_2d}")

    print("Testing 2D to 3D unprojection...")
    u_test, v_test, depth_test = 320, 240, 1.5 # Center pixel, 1.5m depth
    unprojected_3d = unproject_2d_to_3d(u_test, v_test, depth_test, camera_intrinsics)
    print(f"Unprojected 3D point from ({u_test}, {v_test}) with depth {depth_test}: {unprojected_3d}")

    # Clean up dummy files
    import os
    os.remove("data/dummy_rgb.png")
    os.remove("data/dummy_depth.png")
    os.remove("data/dummy_model.ply")


