import torch
import numpy as np
import cv2
import open3d as o3d

from src.cnn_model import SurfEmbCNN
from src.pose_solver import estimate_pose_pnp_ransac, filter_correspondences
from src.data_processing import process_rgbd_image, load_cad_model

class PoseEstimator:
    """
    End-to-end 6D Pose Estimator using SurfEmb approach.
    """
    def __init__(self, model_path, cad_model_path, camera_intrinsics, camera_matrix, dist_coeffs,
                 image_size=(240, 320), device=None):
        """
        Initializes the pose estimator.

        Args:
            model_path (str): Path to the trained SurfEmbCNN model state_dict.
            cad_model_path (str): Path to the object CAD model (PLY file).
            camera_intrinsics (dict): Dictionary with camera intrinsics (fx, fy, cx, cy).
            camera_matrix (numpy.ndarray): 3x3 camera intrinsic matrix.
            dist_coeffs (numpy.ndarray): Distortion coefficients.
            image_size (tuple): Expected input image size (H, W) for the model.
            device (torch.device): Device to run the CNN model on.
        """
        self.device = device if device else torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device for inference: {self.device}")

        self.image_size = image_size
        self.camera_intrinsics = camera_intrinsics
        self.camera_matrix = camera_matrix
        self.dist_coeffs = dist_coeffs

        # Load CNN model
        self.cnn_model = SurfEmbCNN(in_channels=4, out_channels=3, backbone="resnet18", pretrained=False)
        self.cnn_model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.cnn_model.to(self.device)
        self.cnn_model.eval() # Set to evaluation mode
        print(f"Loaded CNN model from {model_path}")

        # Load CAD model
        self.cad_model = load_cad_model(cad_model_path)
        print(f"Loaded CAD model from {cad_model_path}")

    def estimate_pose(self, rgb_image_path, depth_image_path, object_mask=None):
        """
        Estimates the 6D pose of the object from an RGBD image.

        Args:
            rgb_image_path (str): Path to the RGB image.
            depth_image_path (str): Path to the depth image.
            object_mask (numpy.ndarray, optional): Pre-computed binary mask of the object.
                                                   If None, a simple mask (e.g., from depth thresholding)
                                                   or an external detection module would be needed.

        Returns:
            tuple: (rvec, tvec, success) or (None, None, False) if pose estimation fails.
        """
        # 1. Preprocess RGBD image
        rgb_image, depth_image = process_rgbd_image(rgb_image_path, depth_image_path, self.camera_intrinsics)

        # Resize images to model input size if necessary
        rgb_image_resized = cv2.resize(rgb_image, (self.image_size[1], self.image_size[0]))
        depth_image_resized = cv2.resize(depth_image, (self.image_size[1], self.image_size[0]), interpolation=cv2.INTER_NEAREST)

        # If no object mask is provided, create a dummy one (e.g., all ones or based on depth)
        if object_mask is None:
            # A simple mask: pixels with valid depth within a certain range
            # In a real scenario, this would come from an object detection/segmentation model
            object_mask_resized = (depth_image_resized > 0).astype(np.uint8) * 255
        else:
            object_mask_resized = cv2.resize(object_mask, (self.image_size[1], self.image_size[0]), interpolation=cv2.INTER_NEAREST)

        # Prepare input tensor for CNN
        rgb_tensor = torch.from_numpy(rgb_image_resized).permute(2, 0, 1).float() / 255.0
        depth_tensor = torch.from_numpy(depth_image_resized).unsqueeze(0).float() / 1000.0 # Convert to meters
        rgbd_input = torch.cat([rgb_tensor, depth_tensor], dim=0).unsqueeze(0).to(self.device) # Add batch dimension

        # 2. Correspondence Prediction (CNN forward pass)
        with torch.no_grad():
            predicted_correspondence_map_tensor = self.cnn_model(rgbd_input)

        predicted_correspondence_map = predicted_correspondence_map_tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()

        # 3. Filter correspondences and solve pose with PnP+RANSAC
        points_2d, points_3d = filter_correspondences(
            predicted_correspondence_map,
            object_mask_resized,
            depth_image_resized,
            self.camera_intrinsics
        )

        rvec, tvec, inliers, success = estimate_pose_pnp_ransac(
            points_2d,
            points_3d,
            self.camera_matrix,
            self.dist_coeffs
        )

        return rvec, tvec, success

# Example usage (for testing purposes)
if __name__ == "__main__":
    import os
    import config

    # Setup dummy data and model for testing
    if not os.path.exists(config.DATA_DIR): os.makedirs(config.DATA_DIR)
    if not os.path.exists(config.MODELS_DIR): os.makedirs(config.MODELS_DIR)
    if not os.path.exists(config.UTILS_DIR): os.makedirs(config.UTILS_DIR)

    # Create a dummy PLY file
    dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
    dummy_mesh.compute_vertex_normals()
    o3d.io.write_triangle_mesh(config.CAD_MODEL_PATH, dummy_mesh)

    # Create a dummy trained model file
    dummy_model = SurfEmbCNN(in_channels=4, out_channels=3, backbone="resnet18", pretrained=False)
    torch.save(dummy_model.state_dict(), config.MODEL_SAVE_PATH)

    # Create dummy RGB and Depth images
    dummy_rgb = np.zeros((config.SYNTHETIC_IMAGE_SIZE[0], config.SYNTHETIC_IMAGE_SIZE[1], 3), dtype=np.uint8)
    dummy_depth = np.zeros((config.SYNTHETIC_IMAGE_SIZE[0], config.SYNTHETIC_IMAGE_SIZE[1]), dtype=np.uint16)
    cv2.imwrite(os.path.join(config.DATA_DIR, "dummy_rgb.png"), dummy_rgb)
    cv2.imwrite(os.path.join(config.DATA_DIR, "dummy_depth.png"), dummy_depth)

    print("Initializing PoseEstimator...")
    pose_estimator = PoseEstimator(
        model_path=config.MODEL_SAVE_PATH,
        cad_model_path=config.CAD_MODEL_PATH,
        camera_intrinsics=config.CAMERA_INTRINSICS,
        camera_matrix=config.CAMERA_MATRIX,
        dist_coeffs=config.DIST_COEFFS,
        image_size=config.SYNTHETIC_IMAGE_SIZE
    )

    print("Estimating pose...")
    rvec, tvec, success = pose_estimator.estimate_pose(
        rgb_image_path=os.path.join(config.DATA_DIR, "dummy_rgb.png"),
        depth_image_path=os.path.join(config.DATA_DIR, "dummy_depth.png")
    )

    if success:
        print("Pose estimation successful!")
        print(f"Rotation Vector (rvec):\n{rvec}")
        print(f"Translation Vector (tvec):\n{tvec}")
    else:
        print("Pose estimation failed.")

    print("Inference script test finished.")

    # Clean up dummy files
    os.remove(config.CAD_MODEL_PATH)
    os.remove(config.MODEL_SAVE_PATH)
    os.remove(os.path.join(config.DATA_DIR, "dummy_rgb.png"))
    os.remove(os.path.join(config.DATA_DIR, "dummy_depth.png"))
    os.rmdir(config.DATA_DIR)
    os.rmdir(config.MODELS_DIR)
    os.rmdir(config.UTILS_DIR)


