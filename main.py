import argparse
import os
import torch

import config
from dataset import SyntheticPoseDataset
from cnn_model import SurfEmbCNN
from training import train_model
from inference import PoseEstimator
from torch.utils.data import DataLoader
import open3d as o3d
import cv2
import numpy as np

def setup_environment():
    """
    Ensures necessary directories exist.
    """
    os.makedirs(config.DATA_DIR, exist_ok=True)
    os.makedirs(config.MODELS_DIR, exist_ok=True)
    os.makedirs(config.UTILS_DIR, exist_ok=True)

    # Create a dummy render_option.json for Open3D if it doesn't exist
    render_option_path = os.path.join(config.UTILS_DIR, "render_option.json")
    if not os.path.exists(render_option_path):
        with open(render_option_path, "w") as f:
            f.write("{\"background_color\": [0, 0, 0], \"point_size\": 1.0, \"line_width\": 1.0, \"light_on\": true, \"mesh_show_back_face\": true, \"mesh_show_wireframe\": false}")

def main():
    parser = argparse.ArgumentParser(description="SurfEmb 6D Pose Estimation")
    parser.add_argument("--mode", type=str, required=True, choices=["train", "inference"], help="Mode to run: train or inference")
    parser.add_argument("--cad_model", type=str, default=config.CAD_MODEL_PATH, help="Path to the CAD model (PLY file)")
    parser.add_argument("--model_output", type=str, default=config.MODEL_SAVE_PATH, help="Path to save/load the trained CNN model")
    parser.add_argument("--rgb_image", type=str, help="Path to the RGB image for inference")
    parser.add_argument("--depth_image", type=str, help="Path to the depth image for inference")
    parser.add_argument("--num_epochs", type=int, default=config.NUM_EPOCHS, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=config.BATCH_SIZE, help="Batch size for training")
    parser.add_argument("--learning_rate", type=float, default=config.LEARNING_RATE, help="Learning rate for training")

    args = parser.parse_args()

    setup_environment()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    if args.mode == "train":
        print("Running in TRAINING mode...")
        # Ensure CAD model exists for training
        if not os.path.exists(args.cad_model):
            print(f"Error: CAD model not found at {args.cad_model}. Please provide a valid path.")
            # Create a dummy model for demonstration if not found
            print("Creating a dummy CAD model for training demonstration.")
            dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
            dummy_mesh.compute_vertex_normals()
            o3d.io.write_triangle_mesh(args.cad_model, dummy_mesh)
            print(f"Dummy CAD model created at {args.cad_model}")

        dataset = SyntheticPoseDataset(
            cad_model_path=args.cad_model,
            camera_intrinsics=config.CAMERA_INTRINSICS,
            image_size=config.SYNTHETIC_IMAGE_SIZE,
            num_samples=config.SYNTHETIC_NUM_SAMPLES
        )
        dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True)

        model = SurfEmbCNN(in_channels=4, out_channels=3, backbone="resnet18", pretrained=True)
        optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
        criterion = nn.MSELoss()

        train_model(model, dataloader, optimizer, criterion, args.num_epochs, device, save_path=args.model_output)
        print("Training complete. Model saved.")

    elif args.mode == "inference":
        print("Running in INFERENCE mode...")
        if not args.rgb_image or not args.depth_image:
            print("Error: RGB and Depth image paths are required for inference.")
            return
        if not os.path.exists(args.model_output):
            print(f"Error: Trained model not found at {args.model_output}. Please train a model first or provide a valid path.")
            return
        if not os.path.exists(args.cad_model):
            print(f"Error: CAD model not found at {args.cad_model}. Please provide a valid path.")
            return

        pose_estimator = PoseEstimator(
            model_path=args.model_output,
            cad_model_path=args.cad_model,
            camera_intrinsics=config.CAMERA_INTRINSICS,
            camera_matrix=config.CAMERA_MATRIX,
            dist_coeffs=config.DIST_COEFFS,
            image_size=config.SYNTHETIC_IMAGE_SIZE,
            device=device
        )

        rvec, tvec, success = pose_estimator.estimate_pose(
            rgb_image_path=args.rgb_image,
            depth_image_path=args.depth_image
        )

        if success:
            print("Pose estimation successful!")
            print(f"Rotation Vector (rvec):\n{rvec}")
            print(f"Translation Vector (tvec):\n{tvec}")

            # Optional: Visualize the estimated pose
            # This part requires a display, so it might not work in headless environments
            try:
                # Load RGB image for visualization
                rgb_display = cv2.imread(args.rgb_image)
                if rgb_display is None:
                    print(f"Warning: Could not load RGB image for visualization from {args.rgb_image}")
                else:
                    rgb_display = cv2.resize(rgb_display, (config.SYNTHETIC_IMAGE_SIZE[1], config.SYNTHETIC_IMAGE_SIZE[0]))

                    # Project CAD model points to 2D using estimated pose
                    points_3d_model = np.asarray(pose_estimator.cad_model.vertices).astype(np.float32)
                    projected_points, _ = cv2.projectPoints(points_3d_model, rvec, tvec, config.CAMERA_MATRIX, config.DIST_COEFFS)
                    projected_points = np.int32(projected_points).reshape(-1, 2)

                    # Draw projected points on the RGB image
                    for p in projected_points[::100]: # Draw a subset for clarity
                        cv2.circle(rgb_display, tuple(p), 1, (0, 255, 0), -1)

                    cv2.imshow("Estimated Pose", rgb_display)
                    cv2.waitKey(0)
                    cv2.destroyAllWindows()
            except Exception as e:
                print(f"Visualization failed: {e}. This might be due to a headless environment.")

        else:
            print("Pose estimation failed.")

if __name__ == "__main__":
    main()


