# SurfEmb: Surface Embedding-based 6D Pose Estimation

This project implements a Surface Embedding-based 6D Pose Estimation algorithm, inspired by the concept of predicting a pixel's exact 3D coordinate on the surface of an object's original CAD model. The system is designed to work with RGBD images from an Intel D435 camera and utilizes a CAD model in PLY format for the target object.

## Core Idea

SurfEmb operates on a two-step pipeline:

1.  **Correspondence Prediction**: A Convolutional Neural Network (CNN) processes the input image (RGBD) and generates a "correspondence map." For each pixel identified as belonging to the object, this map provides the predicted (x, y, z) coordinate on the object's 3D CAD model.
2.  **Pose Solving**: Given a set of 2D pixel points from the image and their corresponding 3D points on the CAD model, a classic and efficient PnP (Perspective-n-Point) algorithm combined with RANSAC (Random Sample Consensus) is used to robustly calculate the object's 6D pose (3D position and 3D orientation).

## System Requirements

To run this algorithm, you will need:

*   **Operating System**: Ubuntu 22.04 (or similar Linux distribution)
*   **Hardware**: Laptop with at least 32 GB RAM and an NVIDIA RTX 3070Ti GPU (or equivalent CUDA-compatible GPU).
*   **Camera**: Intel D435 (for capturing RGBD images).
*   **Software**: Python 3.8+.

## Installation Instructions

This section outlines the necessary steps to set up your environment and install the required dependencies.

### 1. Clone the Repository

```bash
git clone <repository_url> # Replace with actual repository URL
cd <repository_name>
```

### 2. Create a Virtual Environment (Recommended)

It is highly recommended to use a virtual environment to manage project dependencies.

```bash
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Python Dependencies

The core libraries required are PyTorch for deep learning, OpenCV for image processing, Open3D for 3D data handling, NumPy for numerical operations, and scikit-learn for utility functions.

```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install opencv-python open3d numpy scikit-learn
```

**Note**: The `torch` installation command specifies `cu118` for CUDA 11.8. If your CUDA version is different, please refer to the official PyTorch website for the correct installation command.

### 4. Install Intel RealSense SDK (for D435 Camera)

To interface with the Intel D435 camera, you will need the Intel RealSense SDK. Follow the official installation instructions for your operating system. For Ubuntu, this typically involves:

```bash
sudo apt-key adv --keyserver keys.gnupg.net --recv-key F6E65AC044F8AEEB
sudo add-apt-repository "deb https://librealsense.intel.com/Debian/apt-repo $(lsb_release -cs) main" -u
sudo apt-get update
sudo apt-get install librealsense2-dkms librealsense2-utils librealsense2-dev
```

After installing the SDK, you can install the Python wrapper:

```bash
pip install pyrealsense2
```

## Project Structure (Planned)

*   `data/`: Contains example RGBD images and CAD models.
*   `models/`: Stores trained CNN models.
*   `src/`: Source code for the algorithm components.
    *   `src/data_processing.py`: Utilities for data loading, preprocessing, and CAD model analysis.
    *   `src/cnn_model.py`: Definition of the correspondence prediction CNN.
    *   `src/pose_solver.py`: Implementation of the PnP+RANSAC pose solver.
    *   `src/training.py`: Script for training the CNN.
    *   `src/inference.py`: Script for running inference and pose estimation.
*   `utils/`: Helper functions.
*   `config.py`: Configuration parameters.
*   `main.py`: Main entry point for the application.
*   `README.md`: Project overview and setup instructions (this file).
*   `requirements.txt`: List of Python dependencies.

## Usage

(This section will be updated as the implementation progresses.)

## Contributing

(This section will be updated.)

## License

(This section will be updated.)




## Data Preprocessing and CAD Model Analysis (`src/data_processing.py`)

This module provides essential functionalities for loading and processing RGBD images from the Intel D435 camera, as well as handling the 3D CAD models (PLY files) of the objects. It includes utilities for camera model transformations, such as projecting 3D points to 2D image coordinates and unprojecting 2D pixels with depth information back into 3D space.

### Key Functions:

*   `load_cad_model(model_path)`:
    *   **Purpose**: Loads a 3D CAD model from a specified PLY file. This function is crucial for obtaining the ground truth 3D surface coordinates that the CNN will learn to predict.
    *   **Input**: `model_path` (string) - The absolute path to the `.ply` file.
    *   **Output**: An `open3d.geometry.TriangleMesh` object representing the 3D model. This object contains the vertices, faces, and potentially vertex normals and colors of the model.
    *   **Details**: The function uses Open3D's `read_triangle_mesh` to parse the PLY file. It includes a basic check to ensure that the model contains vertices, preventing further errors if an invalid file is provided.

*   `process_rgbd_image(rgb_image_path, depth_image_path, camera_intrinsics)`:
    *   **Purpose**: Reads and preprocesses both the RGB color image and the corresponding depth image. It handles common image formats and prepares the data for subsequent steps in the pose estimation pipeline.
    *   **Input**: 
        *   `rgb_image_path` (string) - Path to the RGB image file (e.g., `.png`, `.jpg`).
        *   `depth_image_path` (string) - Path to the depth image file (e.g., `.png`, typically 16-bit).
        *   `camera_intrinsics` (dictionary) - A dictionary containing the intrinsic parameters of the camera, specifically `fx`, `fy`, `cx`, and `cy`. These parameters define how the camera projects 3D points onto its 2D image plane.
    *   **Output**: A tuple `(rgb_image, depth_image)`.
        *   `rgb_image` (numpy.ndarray) - The processed RGB image, converted to RGB format (from BGR if loaded by OpenCV).
        *   `depth_image` (numpy.ndarray) - The processed depth image. For Intel D435, depth values are typically in millimeters and stored as `uint16`. This function loads them as-is, and conversion to meters (e.g., by dividing by 1000.0) can be performed as needed based on the specific requirements of the downstream components.
    *   **Details**: Utilizes OpenCV (`cv2.imread`) for efficient image loading. It includes error handling for missing files and ensures the RGB image is in a consistent color space.

*   `project_3d_to_2d(points_3d, camera_intrinsics)`:
    *   **Purpose**: Transforms a set of 3D points from the camera's coordinate system into 2D pixel coordinates on the image plane.
    *   **Input**: 
        *   `points_3d` (numpy.ndarray) - An Nx3 array where N is the number of 3D points, and each row represents an (x, y, z) coordinate.
        *   `camera_intrinsics` (dictionary) - Same camera intrinsic parameters as above (`fx`, `fy`, `cx`, `cy`).
    *   **Output**: A numpy.ndarray of shape Nx2, where each row is a (u, v) pixel coordinate. Points that are behind the camera (z <= 0) are marked as invalid (e.g., -1).
    *   **Details**: Implements the standard pinhole camera model projection equations. It's vectorized for efficiency, handling multiple 3D points simultaneously.

*   `unproject_2d_to_3d(u, v, depth, camera_intrinsics)`:
    *   **Purpose**: Reconstructs a 3D point in the camera's coordinate system from a 2D pixel coordinate and its corresponding depth value.
    *   **Input**: 
        *   `u` (int) - The x-coordinate (column) of the pixel.
        *   `v` (int) - The y-coordinate (row) of the pixel.
        *   `depth` (float) - The depth value at the pixel, typically in meters (ensure consistency with `process_rgbd_image`).
        *   `camera_intrinsics` (dictionary) - Same camera intrinsic parameters as above (`fx`, `fy`, `cx`, `cy`).
    *   **Output**: A numpy.ndarray of shape (3,) representing the (x, y, z) coordinates of the 3D point.
    *   **Details**: This is the inverse operation of `project_3d_to_2d`, allowing the conversion of image-based depth measurements into 3D spatial information.

### Usage Example (within `src/data_processing.py`):

The module includes an `if __name__ == "__main__":` block that demonstrates how to use these functions. It creates dummy RGB and depth images, a dummy PLY model, and then tests the loading, processing, projection, and unprojection functionalities. This block serves as a self-contained test suite for the module.

```python
# Example usage (for testing purposes)
if __name__ == "__main__":
    # Create dummy files for testing
    dummy_rgb = np.zeros((480, 640, 3), dtype=np.uint8)
    dummy_depth = np.zeros((480, 640), dtype=np.uint16)
    cv2.imwrite("data/dummy_rgb.png", dummy_rgb)
    cv2.imwrite("data/dummy_depth.png", dummy_depth)

    # Dummy camera intrinsics (example for D435 at 640x480)
    camera_intrinsics = {
        'fx': 615.0, 'fy': 615.0, 'cx': 320.0, 'cy': 240.0
    }

    print("Testing data loading and processing...")
    try:
        rgb, depth = process_rgbd_image("data/dummy_rgb.png", "data/dummy_depth.png", camera_intrinsics)
        print(f"Loaded RGB image shape: {rgb.shape}, Depth image shape: {depth.shape}")
    except Exception as e:
        print(f"Error processing images: {e}")

    print("Testing CAD model loading...")
    try:
        points = np.array([[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0]], dtype=np.float32)
        colors = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]], dtype=np.float32)
        dummy_mesh = o3d.geometry.TriangleMesh.create_coordinate_frame()
        o3d.io.write_triangle_mesh("data/dummy_model.ply", dummy_mesh)

        model = load_cad_model("data/dummy_model.ply")
        print(f"Loaded CAD model with {len(model.vertices)} vertices.")
    except Exception as e:
        print(f"Error loading CAD model: {e}")

    print("Testing 3D to 2D projection...")
    points_3d_test = np.array([
        [0.1, 0.2, 1.0],  # Point in front of camera
        [-0.5, 0.3, 2.5], # Another point
        [0.0, 0.0, -0.1]  # Point behind camera (should be invalid)
    ])
    projected_2d = project_3d_to_2d(points_3d_test, camera_intrinsics)
    print(f"Projected 2D points:\n{projected_2d}")

    print("Testing 2D to 3D unprojection...")
    u_test, v_test, depth_test = 320, 240, 1.5 # Center pixel, 1.5m depth
    unprojected_3d = unproject_2d_to_3d(u_test, v_test, depth_test, camera_intrinsics)
    print(f"Unprojected 3D point from ({u_test}, {v_test}) with depth {depth_test}: {unprojected_3d}")

    # Clean up dummy files
    import os
    os.remove("data/dummy_rgb.png")
    os.remove("data/dummy_depth.png")
    os.remove("data/dummy_model.ply")
```




## CNN Correspondence Prediction Network (`src/cnn_model.py`)

This module defines the Convolutional Neural Network (CNN) responsible for predicting the 3D surface coordinates for each pixel belonging to the object in the input image. This is the "Correspondence Prediction" step of the SurfEmb pipeline.

### Architecture Overview

The `SurfEmbCNN` model is designed as an encoder-decoder architecture, leveraging pre-trained ResNet models as its backbone for robust feature extraction. The encoder captures hierarchical features from the input RGBD image, while the decoder upsamples these features to produce a dense correspondence map at the original image resolution.

*   **Encoder**: A modified ResNet (e.g., ResNet-18 or ResNet-34) acts as the encoder. The final classification layers of the ResNet are removed, and the initial convolutional layer (`conv1`) is adapted to accept either 3-channel RGB or 4-channel RGBD input, depending on the `in_channels` parameter. If `pretrained` weights are used, the weights for the new input channels are initialized appropriately (e.g., by copying or averaging existing RGB channel weights).
*   **Decoder**: The decoder consists of a series of `ConvTranspose2d` (deconvolutional) layers, interleaved with ReLU activation functions. These layers progressively upsample the feature maps from the encoder, increasing their spatial resolution until they match the input image dimensions. The final layer outputs 3 channels, corresponding to the (x, y, z) coordinates in the object's CAD model space.

### `SurfEmbCNN` Class Details:

*   `__init__(self, in_channels=4, out_channels=3, backbone="resnet18", pretrained=True)`:
    *   **Purpose**: Initializes the CNN model.
    *   **Parameters**:
        *   `in_channels` (int): Specifies the number of input channels. Set to `3` for RGB images or `4` for RGBD images (RGB + Depth). Default is `4`.
        *   `out_channels` (int): Specifies the number of output channels. This is typically `3` for predicting (x, y, z) coordinates. Default is `3`.
        *   `backbone` (str): Defines the pre-trained ResNet model to use as the encoder. Supported options are `"resnet18"` and `"resnet34"`. Default is `"resnet18"`.
        *   `pretrained` (bool): If `True`, the encoder uses weights pre-trained on ImageNet, which can significantly improve performance, especially with limited training data. Default is `True`.
    *   **Initialization Logic**:
        *   Loads the specified ResNet model. If `pretrained` is `True`, it downloads and uses pre-trained weights.
        *   Modifies the first convolutional layer (`self.encoder.conv1`) if `in_channels` is not 3 (e.g., for RGBD input). The weights for the new channels are initialized to ensure compatibility with pre-trained weights.
        *   Removes the average pooling and fully connected layers from the original ResNet, as they are not needed for a dense prediction task.
        *   Constructs the decoder block with `ConvTranspose2d` layers to upsample the features.

*   `forward(self, x)`:
    *   **Purpose**: Defines the forward pass of the network.
    *   **Input**: `x` (torch.Tensor) - An input tensor representing an image batch, with shape `(Batch_Size, in_channels, Height, Width)`.
    *   **Output**: `torch.Tensor` - An output tensor representing the predicted correspondence map, with shape `(Batch_Size, out_channels, Height, Width)`. Each `(x, y)` location in the output map contains the predicted 3D `(x, y, z)` coordinate on the object's surface.

### Usage Example (within `src/cnn_model.py`):

The module includes an `if __name__ == "__main__":` block that demonstrates how to instantiate and test the `SurfEmbCNN` model with both RGB and RGBD inputs. This helps verify that the model's input and output shapes are correct and that it can be initialized without errors.

```python
# Example usage (for testing purposes)
if __name__ == "__main__":
    # Test with RGBD input (4 channels)
    input_tensor_rgbd = torch.randn(1, 4, 256, 256) # Batch size 1, 4 channels, 256x256 image
    model_rgbd = SurfEmbCNN(in_channels=4, backbone="resnet18", pretrained=False)
    output_rgbd = model_rgbd(input_tensor_rgbd)
    print(f"Output shape for RGBD input: {output_rgbd.shape}")
    assert output_rgbd.shape == (1, 3, 256, 256), "RGBD output shape mismatch!"

    # Test with RGB input (3 channels)
    input_tensor_rgb = torch.randn(1, 3, 256, 256) # Batch size 1, 3 channels, 256x256 image
    model_rgb = SurfEmbCNN(in_channels=3, backbone="resnet18", pretrained=False)
    output_rgb = model_rgb(input_tensor_rgb)
    print(f"Output shape for RGB input: {output_rgb.shape}")
    assert output_rgb.shape == (1, 3, 256, 256), "RGB output shape mismatch!"

    print("SurfEmbCNN model tests passed!")
```




## PnP+RANSAC Pose Solver (`src/pose_solver.py`)

This module implements the second crucial step of the SurfEmb pipeline: robust 6D pose estimation using the Perspective-n-Point (PnP) algorithm combined with RANSAC (Random Sample Consensus). It takes the 2D-3D correspondences generated by the CNN and computes the object's rotation and translation relative to the camera.

### Key Functions:

*   `estimate_pose_pnp_ransac(points_2d, points_3d, camera_matrix, dist_coeffs, confidence=0.99, iterations=1000, reprojection_error=8.0)`:
    *   **Purpose**: Computes the 6D pose (rotation vector `rvec` and translation vector `tvec`) of an object given a set of 2D image points and their corresponding 3D model points. RANSAC is employed to make the estimation robust to outliers, which are common in correspondence prediction.
    *   **Input**:
        *   `points_2d` (numpy.ndarray): An Nx2 array of 2D image coordinates `(u, v)` of the detected object features.
        *   `points_3d` (numpy.ndarray): An Nx3 array of corresponding 3D coordinates `(x, y, z)` from the object's CAD model.
        *   `camera_matrix` (numpy.ndarray): A 3x3 intrinsic camera matrix `[[fx, 0, cx], [0, fy, cy], [0, 0, 1]]`.
        *   `dist_coeffs` (numpy.ndarray): A vector of distortion coefficients (e.g., `[k1, k2, p1, p2, k3]`). If the camera is calibrated and distortion-free, this can be an array of zeros.
        *   `confidence` (float, optional): The confidence level for the RANSAC algorithm. Default is `0.99`.
        *   `iterations` (int, optional): The maximum number of iterations for RANSAC. Default is `1000`.
        *   `reprojection_error` (float, optional): The maximum allowed reprojection error (in pixels) for a point to be considered an inlier during RANSAC. Default is `8.0`.
    *   **Output**: A tuple `(rvec, tvec, inliers, success)`.
        *   `rvec` (numpy.ndarray): A 3x1 rotation vector (Rodrigues vector) representing the object's orientation.
        *   `tvec` (numpy.ndarray): A 3x1 translation vector representing the object's position.
        *   `inliers` (numpy.ndarray): An array of indices of the points that were identified as inliers by RANSAC.
        *   `success` (bool): `True` if the pose estimation was successful (i.e., enough inliers were found), `False` otherwise.
    *   **Details**: This function wraps OpenCV's `cv2.solvePnPRansac` function. It requires at least 4 corresponding points to function correctly. The `cv2.SOLVEPNP_EPNP` flag is used for the PnP algorithm, which is generally a robust and accurate choice.

*   `filter_correspondences(correspondence_map, object_mask, depth_image, camera_intrinsics, depth_scale=1000.0)`:
    *   **Purpose**: Extracts valid 2D-3D correspondence pairs from the CNN's output `correspondence_map` and the input `depth_image`, using an `object_mask` to focus only on relevant pixels. This function prepares the data for the PnP algorithm.
    *   **Input**:
        *   `correspondence_map` (numpy.ndarray): A HxWx3 array, typically the output of the `SurfEmbCNN`, where each pixel `(v, u)` contains the predicted 3D `(X, Y, Z)` coordinate on the CAD model's surface.
        *   `object_mask` (numpy.ndarray): A HxW binary mask (e.g., from an instance segmentation model) where non-zero values indicate pixels belonging to the object of interest.
        *   `depth_image` (numpy.ndarray): A HxW depth image, typically `uint16` values representing depth in millimeters.
        *   `camera_intrinsics` (dict): A dictionary containing camera intrinsic parameters (`fx`, `fy`, `cx`, `cy`).
        *   `depth_scale` (float, optional): A scaling factor to convert raw depth values (e.g., millimeters) into meters. Default is `1000.0`.
    *   **Output**: A tuple `(valid_points_2d, valid_points_3d)`.
        *   `valid_points_2d` (numpy.ndarray): A Kx2 array of 2D image pixel coordinates `(u, v)` that belong to the object and have valid depth and CNN predictions.
        *   `valid_points_3d` (numpy.ndarray): A Kx3 array of corresponding 3D model points `(x, y, z)` predicted by the CNN for `valid_points_2d`.
    *   **Details**: This function iterates through each pixel. If a pixel is part of the `object_mask` and has a valid depth reading, its 2D coordinate `(u, v)` is paired with the 3D model coordinate predicted by the `correspondence_map` at that pixel. This forms the input for the PnP solver.

### Usage Example (within `src/pose_solver.py`):

The module includes an `if __name__ == "__main__":` block that demonstrates how to use these functions. It generates dummy `correspondence_map`, `object_mask`, and `depth_image` data, then filters correspondences and attempts to estimate the pose using `estimate_pose_pnp_ransac`. This block serves as a self-contained test suite for the module.

```python
# Example usage (for testing purposes)
if __name__ == "__main__":
    # Dummy data for testing
    H, W = 256, 256
    dummy_correspondence_map = np.random.rand(H, W, 3).astype(np.float32) * 10 # Random 3D points
    dummy_object_mask = np.zeros((H, W), dtype=np.uint8)
    dummy_object_mask[H//4:3*H//4, W//4:3*W//4] = 255 # A square mask
    dummy_depth_image = np.random.randint(100, 2000, size=(H, W), dtype=np.uint16) # Depth in mm
    dummy_depth_image[dummy_object_mask == 0] = 0 # No depth outside object

    dummy_camera_intrinsics = {
        "fx": 615.0, "fy": 615.0, "cx": 320.0, "cy": 240.0
    }

    # Create a dummy camera matrix and distortion coefficients
    dummy_camera_matrix = np.array([
        [dummy_camera_intrinsics["fx"], 0, dummy_camera_intrinsics["cx"]],
        [0, dummy_camera_intrinsics["fy"], dummy_camera_intrinsics["cy"]],
        [0, 0, 1]
    ], dtype=np.float32)
    dummy_dist_coeffs = np.zeros((4, 1), dtype=np.float32) # No distortion

    print("Testing correspondence filtering...")
    points_2d, points_3d = filter_correspondences(
        dummy_correspondence_map,
        dummy_object_mask,
        dummy_depth_image,
        dummy_camera_intrinsics
    )
    print(f"Filtered {len(points_2d)} 2D-3D correspondences.")
    assert len(points_2d) == np.sum(dummy_object_mask > 0), "Filtering count mismatch!"

    print("Testing PnP with RANSAC...")
    if len(points_2d) >= 4:
        rvec, tvec, inliers, success = estimate_pose_pnp_ransac(
            points_2d,
            points_3d,
            dummy_camera_matrix,
            dummy_dist_coeffs
        )

        if success:
            print("Pose estimation successful!")
            print(f"Rotation Vector (rvec):\n{rvec}")
            print(f"Translation Vector (tvec):\n{tvec}")
            print(f"Number of inliers: {len(inliers) if inliers is not None else 0}")
        else:
            print("Pose estimation failed.")
    else:
        print("Not enough valid correspondences to run PnP.")

    print("PnP+RANSAC pose solver tests completed!")
```




## Synthetic Data Generation (`src/dataset.py`)

This module provides the `SyntheticPoseDataset` class, a PyTorch Dataset implementation crucial for generating synthetic RGBD images, corresponding object masks, and ground truth 3D correspondence maps. This synthetic data is essential for training the `SurfEmbCNN` model, especially when real-world annotated data is scarce.

### Core Concept: Synthetic Data for Training

Training a deep learning model for 6D pose estimation requires a large amount of data with ground truth 3D coordinates for each pixel on the object's surface. Manually annotating such data is extremely time-consuming and often impractical. Synthetic data generation offers a scalable solution by rendering a 3D CAD model into 2D images from various viewpoints and distances, while simultaneously capturing the precise 3D coordinates of each visible surface point.

### `SyntheticPoseDataset` Class Details:

*   `__init__(self, cad_model_path, camera_intrinsics, image_size=(256, 256), num_samples=1000, min_distance=0.5, max_distance=1.5, fov_x=60, fov_y=45)`:
    *   **Purpose**: Initializes the synthetic dataset generator.
    *   **Parameters**:
        *   `cad_model_path` (str): Path to the object's 3D CAD model in PLY format. This model is used for rendering.
        *   `camera_intrinsics` (dict): A dictionary containing the camera's intrinsic parameters (`fx`, `fy`, `cx`, `cy`). These define the virtual camera used for rendering.
        *   `image_size` (tuple): The desired `(height, width)` of the generated RGBD images, masks, and correspondence maps. Default is `(256, 256)`.
        *   `num_samples` (int): The total number of synthetic samples (image-mask-correspondence triplets) to generate. Default is `1000`.
        *   `min_distance` (float): The minimum distance (in meters) from which the object will be rendered. This helps control the scale and apparent size of the object in the synthetic images. Default is `0.5`.
        *   `max_distance` (float): The maximum distance (in meters) from which the object will be rendered. Default is `1.5`.
        *   `fov_x` (float): Horizontal field of view of the virtual camera in degrees. Default is `60`.
        *   `fov_y` (float): Vertical field of view of the virtual camera in degrees. Default is `45`.
    *   **Initialization Logic**:
        *   Loads the CAD model using Open3D and computes vertex normals for realistic rendering.
        *   Sets up an `open3d.visualization.VisualizerOffscreen` for headless rendering. This allows generating images without a graphical display.
        *   Configures the virtual camera's intrinsic parameters within the Open3D visualizer.
        *   Loads rendering options from `utils/render_option.json` to control aspects like background color, lighting, and mesh display.

*   `_load_and_preprocess_cad_model(self, cad_model_path)`:
    *   **Purpose**: Helper function to load the CAD model and perform initial preprocessing like computing vertex normals.

*   `_generate_random_pose(self)`:
    *   **Purpose**: Generates a random 6D pose (rotation and translation) for the object within the specified distance and field-of-view constraints. This ensures variety in the training data.
    *   **Details**: Uses `scipy.spatial.transform.Rotation` to generate random rotations and uniformly samples translation values for x, y, and z axes, ensuring the object remains within the camera's view frustum.

*   `_render_synthetic_data(self, pose_matrix)`:
    *   **Purpose**: Renders an RGB image, a depth image, an object mask, and the crucial ground truth 3D correspondence map for a given object pose.
    *   **Input**: `pose_matrix` (numpy.ndarray): A 4x4 homogeneous transformation matrix representing the object's pose relative to the camera.
    *   **Output**: A tuple `(rgb, depth_uint16, object_mask, correspondence_map, pose_matrix)`.
        *   `rgb` (numpy.ndarray): The rendered RGB image.
        *   `depth_uint16` (numpy.ndarray): The rendered depth image, typically in `uint16` millimeters.
        *   `object_mask` (numpy.ndarray): A binary mask indicating which pixels belong to the rendered object.
        *   `correspondence_map` (numpy.ndarray): A HxWx3 array where each pixel `(v, u)` within the `object_mask` contains the precise `(x, y, z)` coordinate of that point on the CAD model's surface. **Note**: The current implementation provides a placeholder for the `correspondence_map` (filling with the object's center). A full implementation would require advanced rendering techniques (e.g., rendering vertex IDs or using custom shaders) to accurately obtain pixel-wise 3D coordinates from the model. This is a critical component for effective training.
    *   **Details**: This function uses the Open3D offscreen renderer. It transforms the CAD model according to the `pose_matrix` and then captures the RGB and depth buffers. The object mask is derived from valid depth values. The generation of the ground truth correspondence map is highlighted as an area requiring more sophisticated rendering for a production-ready system.

*   `__len__(self)`:
    *   **Purpose**: Returns the total number of synthetic samples (`num_samples`).

*   `__getitem__(self, idx)`:
    *   **Purpose**: Generates and returns a single synthetic data sample (RGBD input, object mask, ground truth correspondence map) for a given index.
    *   **Output**: A tuple `(rgbd_input, object_mask_tensor, correspondence_map_tensor)`.
        *   `rgbd_input` (torch.Tensor): A 4-channel tensor combining the RGB image and the depth image.
        *   `object_mask_tensor` (torch.Tensor): A 1-channel tensor representing the object mask.
        *   `correspondence_map_tensor` (torch.Tensor): A 3-channel tensor containing the ground truth 3D coordinates for each pixel on the object's surface.
    *   **Details**: Calls `_generate_random_pose` and `_render_synthetic_data` to create the sample, then converts the NumPy arrays to PyTorch tensors and adjusts their dimensions for model input.

### Usage Example (within `src/dataset.py`):

The module includes an `if __name__ == "__main__":` block that demonstrates how to initialize the `SyntheticPoseDataset`, retrieve a sample, and verify the shapes of the generated tensors. It also includes setup for dummy CAD models and rendering options for self-contained testing.

```python
# Example usage (for testing purposes)
if __name__ == "__main__":
    # Create a dummy PLY file for testing
    if not os.path.exists("data"): os.makedirs("data")
    dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
    dummy_mesh.compute_vertex_normals()
    o3d.io.write_triangle_mesh("data/dummy_model.ply", dummy_mesh)

    # Create a dummy render_option.json for Open3D
    if not os.path.exists("utils"): os.makedirs("utils")
    with open("utils/render_option.json", "w") as f:
        f.write("{\"background_color\": [0, 0, 0], \"point_size\": 1.0, \"line_width\": 1.0}")

    camera_intrinsics = {
        'fx': 615.0, 'fy': 615.0, 'cx': 320.0, 'cy': 240.0
    }
    image_size = (240, 320) # H, W

    print("Initializing SyntheticPoseDataset...")
    dataset = SyntheticPoseDataset(
        cad_model_path="data/dummy_model.ply",
        camera_intrinsics=camera_intrinsics,
        image_size=image_size,
        num_samples=5
    )

    print(f"Dataset size: {len(dataset)}")

    print("Fetching a sample...")
    rgbd_input, object_mask, correspondence_map = dataset[0]

    print(f"RGBD Input shape: {rgbd_input.shape}")        # Expected: (4, H, W)
    print(f"Object Mask shape: {object_mask.shape}")      # Expected: (1, H, W)
    print(f"Correspondence Map shape: {correspondence_map.shape}") # Expected: (3, H, W)

    assert rgbd_input.shape == (4, image_size[0], image_size[1])
    assert object_mask.shape == (1, image_size[0], image_size[1])
    assert correspondence_map.shape == (3, image_size[0], image_size[1])

    print("SyntheticPoseDataset test passed!")

    # Clean up dummy files
    os.remove("data/dummy_model.ply")
    os.remove("utils/render_option.json")
    os.rmdir("data")
    os.rmdir("utils")
```




## Training Pipeline (`src/training.py`)

This module contains the `train_model` function, which orchestrates the training process for the `SurfEmbCNN` using the synthetic data generated by `SyntheticPoseDataset`. It defines the core loop for optimizing the CNN to predict accurate 3D correspondence maps.

### Training Objective

The primary goal of training is to minimize the difference between the CNN-predicted 3D coordinates and the ground truth 3D coordinates on the object's surface for each pixel belonging to the object. This is typically achieved using a Mean Squared Error (MSE) loss function, applied only to the pixels within the object's mask.

### `train_model` Function Details:

*   `train_model(model, dataloader, optimizer, criterion, num_epochs, device, save_path="models/surfemb_model.pth")`:
    *   **Purpose**: Executes the training loop for the `SurfEmbCNN` model.
    *   **Parameters**:
        *   `model` (nn.Module): An instance of the `SurfEmbCNN` model to be trained.
        *   `dataloader` (DataLoader): A PyTorch `DataLoader` instance, typically created from `SyntheticPoseDataset`, which provides batches of `(rgbd_input, object_mask, gt_correspondence_map)`.
        *   `optimizer` (optim.Optimizer): The optimization algorithm (e.g., `torch.optim.Adam`) used to update the model's weights based on the calculated loss.
        *   `criterion` (nn.Module): The loss function (e.g., `nn.MSELoss`) that quantifies the error between predicted and ground truth correspondence maps.
        *   `num_epochs` (int): The total number of training epochs (full passes over the dataset).
        *   `device` (torch.device): Specifies whether to train on the CPU or a CUDA-enabled GPU (e.g., `torch.device("cuda")`).
        *   `save_path` (str, optional): The file path where the trained model's state dictionary will be saved. Checkpoints are also saved per epoch. Default is `"models/surfemb_model.pth"`.
    *   **Training Loop Logic**:
        1.  **Model Setup**: Sets the model to training mode (`model.train()`) and moves it to the specified `device`.
        2.  **Epoch Iteration**: Loops through the specified number of `num_epochs`.
        3.  **Batch Iteration**: For each epoch, iterates through batches provided by the `dataloader`.
        4.  **Data Transfer**: Moves `rgbd_input`, `object_mask`, and `gt_correspondence_map` to the `device`.
        5.  **Zero Gradients**: Clears the gradients of all optimized tensors (`optimizer.zero_grad()`) to prevent accumulation from previous batches.
        6.  **Forward Pass**: Feeds the `rgbd_input` through the `model` to get `predicted_correspondence_map`.
        7.  **Masking**: Applies the `object_mask` to both the `gt_correspondence_map` and `predicted_correspondence_map`. This ensures that the loss is calculated only for pixels that actually belong to the object, ignoring background pixels.
        8.  **Loss Calculation**: Computes the `criterion` (e.g., MSE loss) between the masked predicted and ground truth correspondence maps.
        9.  **Backward Pass**: Computes gradients of the loss with respect to model parameters (`loss.backward()`).
        10. **Optimizer Step**: Updates the model's weights using the calculated gradients (`optimizer.step()`).
        11. **Logging**: Prints the loss for the current step and the average loss for each epoch.
        12. **Model Saving**: Saves the model's `state_dict` at the end of each epoch and a final model after all epochs are complete. This allows for resuming training or using the best-performing model.

### Usage Example (within `src/training.py`):

The module includes an `if __name__ == "__main__":` block that demonstrates a complete training setup. It initializes a `SyntheticPoseDataset`, `SurfEmbCNN` model, `Adam` optimizer, and `MSELoss` criterion, then calls `train_model` to start a short training run. This block is useful for verifying the training pipeline's functionality.

```python
# Example usage (for testing purposes)
if __name__ == "__main__":
    # Create a dummy PLY file for testing
    if not os.path.exists("data"): os.makedirs("data")
    dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
    dummy_mesh.compute_vertex_normals()
    o3d.io.write_triangle_mesh("data/dummy_model.ply", dummy_mesh)

    # Create a dummy render_option.json for Open3D
    if not os.path.exists("utils"): os.makedirs("utils")
    with open("utils/render_option.json", "w") as f:
        f.write("{\"background_color\": [0, 0, 0], \"point_size\": 1.0, \"line_width\": 1.0}")

    camera_intrinsics = {
        'fx': 615.0, 'fy': 615.0, 'cx': 320.0, 'cy': 240.0
    }
    image_size = (240, 320) # H, W

    # Hyperparameters
    num_epochs = 2
    batch_size = 2
    learning_rate = 0.001

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Dataset and DataLoader
    print("Creating synthetic dataset...")
    dataset = SyntheticPoseDataset(
        cad_model_path="data/dummy_model.ply",
        camera_intrinsics=camera_intrinsics,
        image_size=image_size,
        num_samples=batch_size * 5 # Small number for quick test
    )
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    # Model, Optimizer, Loss
    print("Initializing model...")
    model = SurfEmbCNN(in_channels=4, out_channels=3, backbone="resnet18", pretrained=False)
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.MSELoss() # Mean Squared Error Loss for regression of 3D coordinates

    # Train the model
    print("Starting training...")
    train_model(model, dataloader, optimizer, criterion, num_epochs, device, save_path="models/test_surfemb_model.pth")

    print("Training script test finished.")

    # Clean up dummy files
    os.remove("data/dummy_model.ply")
    os.remove("utils/render_option.json")
    os.rmdir("data")
    os.rmdir("utils")
    # Remove generated model checkpoints
    for epoch in range(num_epochs):
        if os.path.exists(f"models/test_surfemb_model_epoch_{epoch+1}.pth"):
            os.remove(f"models/test_surfemb_model_epoch_{epoch+1}.pth")
    if os.path.exists("models/test_surfemb_model.pth"):
        os.remove("models/test_surfemb_model.pth")
    os.rmdir("models")
```




### Update on Ground Truth Correspondence Map Generation:

Previously, the `_render_synthetic_data` function in `src/dataset.py` used a placeholder for the `correspondence_map`. This has been updated to a more robust method. Now, for each pixel within the object mask, its 2D image coordinates and depth value are unprojected back into 3D space (camera frame). These 3D points are then transformed from the camera frame to the object's local coordinate system using the inverse of the object's pose. This process accurately generates the ground truth 3D coordinates on the CAD model's surface for each visible pixel, which is essential for training the `SurfEmbCNN`.




## Configuration (`config.py`)

The `config.py` file centralizes all configurable parameters for the SurfEmb project. This includes camera intrinsics, model paths, training hyperparameters, and inference settings. By modifying this single file, users can easily adapt the algorithm to different cameras, objects, and training regimes without altering the core code.

### Key Configuration Parameters:

*   **`CAMERA_INTRINSICS` (dict)**: A dictionary holding the intrinsic parameters of the Intel D435 camera. These are crucial for both synthetic data generation and the PnP algorithm. It includes:
    *   `fx`, `fy`: Focal lengths in pixels.
    *   `cx`, `cy`: Principal point coordinates in pixels.
    *   **Note**: Users should replace the example values with their camera's actual calibrated intrinsics for accurate results.

*   **`CAMERA_MATRIX` (numpy.ndarray)**: The 3x3 camera intrinsic matrix derived directly from `CAMERA_INTRINSICS`. This matrix is used by OpenCV functions like `cv2.solvePnPRansac`.

*   **`DIST_COEFFS` (numpy.ndarray)**: A 1xN or Nx1 array of distortion coefficients. For an undistorted camera or if distortion correction is handled externally, this can be an array of zeros. Users should provide their camera's calibrated distortion coefficients if applicable.

*   **`CAD_MODEL_PATH` (str)**: The file path to the object's 3D CAD model in PLY format. This model is used for synthetic data generation and as the reference for pose estimation.

*   **Training Parameters**:
    *   `NUM_EPOCHS` (int): The total number of training epochs for the CNN.
    *   `BATCH_SIZE` (int): The batch size used during training.
    *   `LEARNING_RATE` (float): The learning rate for the optimizer during CNN training.
    *   `MODEL_SAVE_PATH` (str): The path where the trained CNN model's state dictionary will be saved.

*   **Synthetic Dataset Parameters**:
    *   `SYNTHETIC_IMAGE_SIZE` (tuple): The `(Height, Width)` of the synthetic images generated for training.
    *   `SYNTHETIC_NUM_SAMPLES` (int): The total number of synthetic samples to generate for the dataset.
    *   `SYNTHETIC_MIN_DISTANCE`, `SYNTHETIC_MAX_DISTANCE` (float): The minimum and maximum distances (in meters) from which the object is rendered in synthetic images.
    *   `SYNTHETIC_FOV_X`, `SYNTHETIC_FOV_Y` (float): The horizontal and vertical fields of view (in degrees) for the virtual camera used in synthetic data generation.

*   **Inference Parameters**:
    *   `CONFIDENCE_THRESHOLD` (float): A placeholder for object detection confidence (if an external detection module is integrated).
    *   `REPROJECTION_ERROR_PNP` (float): The maximum allowed reprojection error in pixels for PnP RANSAC inlier selection.
    *   `PNP_CONFIDENCE` (float): The confidence level for the PnP RANSAC algorithm.
    *   `PNP_ITERATIONS` (int): The maximum number of iterations for PnP RANSAC.

*   **Paths**:
    *   `DATA_DIR`, `MODELS_DIR`, `UTILS_DIR`: Directory paths for storing data, trained models, and utility files, respectively. The `setup_environment` function in `main.py` ensures these directories are created if they don't exist.

### Example `config.py` Content:

```python
import numpy as np

# Camera Intrinsics for Intel D435 (example values for 640x480 resolution)
# You should replace these with your camera's calibrated intrinsics
CAMERA_INTRINSICS = {
    'fx': 615.0,  # Focal length x
    'fy': 615.0,  # Focal length y
    'cx': 320.0,  # Principal point x
    'cy': 240.0   # Principal point y
}

# Camera Matrix (derived from intrinsics)
CAMERA_MATRIX = np.array([
    [CAMERA_INTRINSICS['fx'], 0, CAMERA_INTRINSICS['cx']],
    [0, CAMERA_INTRINSICS['fy'], CAMERA_INTRINSICS['cy']],
    [0, 0, 1]
], dtype=np.float32)

# Distortion Coefficients (example: no distortion)
# Replace with your camera's calibrated distortion coefficients
DIST_COEFFS = np.zeros((4, 1), dtype=np.float32)

# Model Parameters
CAD_MODEL_PATH = "data/object160_140.ply" # Path to your object's CAD model

# Training Parameters
NUM_EPOCHS = 50
BATCH_SIZE = 8
LEARNING_RATE = 0.001
MODEL_SAVE_PATH = "models/surfemb_model.pth"

# Synthetic Dataset Parameters
SYNTHETIC_IMAGE_SIZE = (240, 320) # Height, Width
SYNTHETIC_NUM_SAMPLES = 10000 # Number of synthetic samples for training
SYNTHETIC_MIN_DISTANCE = 0.5 # meters
SYNTHETIC_MAX_DISTANCE = 1.5 # meters
SYNTHETIC_FOV_X = 60 # degrees
SYNTHETIC_FOV_Y = 45 # degrees

# Inference Parameters
CONFIDENCE_THRESHOLD = 0.5 # For object detection (if used)
REPROJECTION_ERROR_PNP = 8.0 # Pixels
PNP_CONFIDENCE = 0.99
PNP_ITERATIONS = 1000

# Paths
DATA_DIR = "data"
MODELS_DIR = "models"
UTILS_DIR = "utils"

# Ensure directories exist
import os
for d in [DATA_DIR, MODELS_DIR, UTILS_DIR]:
    os.makedirs(d, exist_ok=True)
```




## Inference Pipeline (`src/inference.py`)

This module provides the `PoseEstimator` class, which integrates the trained CNN model with the PnP+RANSAC pose solver to perform end-to-end 6D pose estimation on real RGBD images. It encapsulates the entire inference pipeline, from image preprocessing to final pose calculation.

### `PoseEstimator` Class Details:

*   `__init__(self, model_path, cad_model_path, camera_intrinsics, camera_matrix, dist_coeffs, image_size=(240, 320), device=None)`:
    *   **Purpose**: Initializes the pose estimator by loading the trained CNN model and the object's CAD model, and setting up camera parameters.
    *   **Parameters**:
        *   `model_path` (str): Path to the saved state dictionary of the trained `SurfEmbCNN` model.
        *   `cad_model_path` (str): Path to the object's 3D CAD model (PLY file).
        *   `camera_intrinsics` (dict): Dictionary containing camera intrinsic parameters (`fx`, `fy`, `cx`, `cy`).
        *   `camera_matrix` (numpy.ndarray): The 3x3 camera intrinsic matrix.
        *   `dist_coeffs` (numpy.ndarray): The camera's distortion coefficients.
        *   `image_size` (tuple): The `(Height, Width)` that the CNN model expects as input. Default is `(240, 320)`.
        *   `device` (torch.device, optional): The computing device (e.g., `"cuda"` or `"cpu"`) on which to run the CNN model. If `None`, it defaults to CUDA if available, otherwise CPU.
    *   **Initialization Logic**:
        *   Sets the device for computation.
        *   Loads the `SurfEmbCNN` model, loads its pre-trained weights from `model_path`, and sets the model to evaluation mode (`model.eval()`).
        *   Loads the object's CAD model using `src.data_processing.load_cad_model`.

*   `estimate_pose(self, rgb_image_path, depth_image_path, object_mask=None)`:
    *   **Purpose**: Performs 6D pose estimation for an object given an RGBD image.
    *   **Input**:
        *   `rgb_image_path` (str): Path to the input RGB image file.
        *   `depth_image_path` (str): Path to the input depth image file.
        *   `object_mask` (numpy.ndarray, optional): An optional pre-computed binary mask of the object in the image. If `None`, a simple mask (e.g., based on valid depth pixels) is generated. In a real application, this would typically come from an object detection or instance segmentation model (e.g., YOLOv5, SAM6D).
    *   **Output**: A tuple `(rvec, tvec, success)`.
        *   `rvec` (numpy.ndarray): The 3x1 rotation vector if pose estimation is successful.
        *   `tvec` (numpy.ndarray): The 3x1 translation vector if pose estimation is successful.
        *   `success` (bool): `True` if pose estimation was successful, `False` otherwise.
    *   **Inference Pipeline Steps**:
        1.  **Image Preprocessing**: Loads and preprocesses the RGB and depth images using `src.data_processing.process_rgbd_image`. Images are resized to the model's expected input dimensions.
        2.  **Object Masking**: If no `object_mask` is provided, a basic mask is created from valid depth pixels. For production use, an external object detection/segmentation module would provide a more accurate mask.
        3.  **CNN Forward Pass**: The preprocessed RGBD input is fed into the `SurfEmbCNN` to predict the `correspondence_map`. This step is performed without gradient computation (`torch.no_grad()`) as it's inference.
        4.  **Correspondence Filtering**: The `correspondence_map`, `object_mask`, and `depth_image` are used by `src.pose_solver.filter_correspondences` to extract valid 2D-3D correspondence pairs.
        5.  **PnP+RANSAC Pose Solving**: The extracted 2D-3D correspondences are passed to `src.pose_solver.estimate_pose_pnp_ransac` to compute the final 6D pose.

### Usage Example (within `src/inference.py`):

The module includes an `if __name__ == "__main__":` block that demonstrates how to initialize the `PoseEstimator` and perform a pose estimation on dummy RGBD images. It also includes setup for dummy CAD models and a trained model for self-contained testing.

```python
# Example usage (for testing purposes)
if __name__ == "__main__":
    import os
    import config

    # Setup dummy data and model for testing
    if not os.path.exists(config.DATA_DIR): os.makedirs(config.DATA_DIR)
    if not os.path.exists(config.MODELS_DIR): os.makedirs(config.MODELS_DIR)
    if not os.path.exists(config.UTILS_DIR): os.makedirs(config.UTILS_DIR)

    # Create a dummy PLY file
    dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
    dummy_mesh.compute_vertex_normals()
    o3d.io.write_triangle_mesh(config.CAD_MODEL_PATH, dummy_mesh)

    # Create a dummy trained model file
    dummy_model = SurfEmbCNN(in_channels=4, out_channels=3, backbone="resnet18", pretrained=False)
    torch.save(dummy_model.state_dict(), config.MODEL_SAVE_PATH)

    # Create dummy RGB and Depth images
    dummy_rgb = np.zeros((config.SYNTHETIC_IMAGE_SIZE[0], config.SYNTHETIC_IMAGE_SIZE[1], 3), dtype=np.uint8)
    dummy_depth = np.zeros((config.SYNTHETIC_IMAGE_SIZE[0], config.SYNTHETIC_IMAGE_SIZE[1]), dtype=np.uint16)
    cv2.imwrite(os.path.join(config.DATA_DIR, "dummy_rgb.png"), dummy_rgb)
    cv2.imwrite(os.path.join(config.DATA_DIR, "dummy_depth.png"), dummy_depth)

    print("Initializing PoseEstimator...")
    pose_estimator = PoseEstimator(
        model_path=config.MODEL_SAVE_PATH,
        cad_model_path=config.CAD_MODEL_PATH,
        camera_intrinsics=config.CAMERA_INTRINSICS,
        camera_matrix=config.CAMERA_MATRIX,
        dist_coeffs=config.DIST_COEFFS,
        image_size=config.SYNTHETIC_IMAGE_SIZE
    )

    print("Estimating pose...")
    rvec, tvec, success = pose_estimator.estimate_pose(
        rgb_image_path=os.path.join(config.DATA_DIR, "dummy_rgb.png"),
        depth_image_path=os.path.join(config.DATA_DIR, "dummy_depth.png")
    )

    if success:
        print("Pose estimation successful!")
        print(f"Rotation Vector (rvec):\n{rvec}")
        print(f"Translation Vector (tvec):\n{tvec}")
    else:
        print("Pose estimation failed.")

    print("Inference script test finished.")

    # Clean up dummy files
    os.remove(config.CAD_MODEL_PATH)
    os.remove(config.MODEL_SAVE_PATH)
    os.remove(os.path.join(config.DATA_DIR, "dummy_rgb.png"))
    os.remove(os.path.join(config.DATA_DIR, "dummy_depth.png"))
    os.rmdir(config.DATA_DIR)
    os.rmdir(config.MODELS_DIR)
    os.rmdir(config.UTILS_DIR)
```




## Main Application Entry Point (`main.py`)

The `main.py` script serves as the primary entry point for the SurfEmb 6D Pose Estimation application. It orchestrates the entire workflow, allowing users to either train the CNN model or perform pose estimation inference using a trained model. It utilizes `argparse` for command-line argument parsing, making it flexible and easy to use.

### Key Functionalities:

*   **Command-line Interface**: Provides a user-friendly command-line interface to select between `train` and `inference` modes and configure various parameters.
*   **Environment Setup**: Ensures that necessary directories (`data`, `models`, `utils`) are created and that a default `render_option.json` file exists for Open3D rendering.
*   **Device Management**: Automatically detects and utilizes a CUDA-enabled GPU if available, otherwise falls back to CPU.

### Usage Modes:

#### 1. Training Mode

To train the `SurfEmbCNN` model, run `main.py` with the `--mode train` argument. This will generate synthetic data using the `SyntheticPoseDataset`, initialize the CNN, and start the training process.

**Command-line Arguments for Training**:

*   `--mode train`: Specifies the training mode.
*   `--cad_model <path/to/your/model.ply>` (optional): Path to the object's CAD model. Defaults to `config.CAD_MODEL_PATH`. If the specified CAD model is not found, a dummy sphere model will be created for demonstration purposes.
*   `--model_output <path/to/save/model.pth>` (optional): Path where the trained model's state dictionary will be saved. Defaults to `config.MODEL_SAVE_PATH`.
*   `--num_epochs <int>` (optional): Number of training epochs. Defaults to `config.NUM_EPOCHS`.
*   `--batch_size <int>` (optional): Batch size for training. Defaults to `config.BATCH_SIZE`.
*   `--learning_rate <float>` (optional): Learning rate for the optimizer. Defaults to `config.LEARNING_RATE`.

**Example Training Command**:

```bash
python main.py --mode train --cad_model data/my_object.ply --num_epochs 50 --batch_size 16
```

#### 2. Inference Mode

To perform 6D pose estimation on RGBD images, run `main.py` with the `--mode inference` argument. This requires a pre-trained model and paths to the RGB and depth images.

**Command-line Arguments for Inference**:

*   `--mode inference`: Specifies the inference mode.
*   `--rgb_image <path/to/rgb.png>` (required): Path to the input RGB image.
*   `--depth_image <path/to/depth.png>` (required): Path to the input depth image.
*   `--cad_model <path/to/your/model.ply>` (optional): Path to the object's CAD model. Defaults to `config.CAD_MODEL_PATH`.
*   `--model_output <path/to/trained/model.pth>` (optional): Path to the trained CNN model to be loaded for inference. Defaults to `config.MODEL_SAVE_PATH`.

**Example Inference Command**:

```bash
python main.py --mode inference --rgb_image data/test_rgb.png --depth_image data/test_depth.png --model_output models/surfemb_model.pth
```

**Visualization (Optional)**:

The inference mode includes an optional visualization step that projects the 3D CAD model onto the 2D RGB image using the estimated pose. This helps visually verify the accuracy of the pose estimation. This feature might not work in headless environments.

### `setup_environment()` Function:

*   **Purpose**: A helper function called at the beginning of `main.py` to ensure that all necessary directories (`data`, `models`, `utils`) exist. It also creates a default `utils/render_option.json` file if it's missing, which is used by Open3D for rendering.

### Overall Flow:

1.  **Argument Parsing**: Command-line arguments are parsed to determine the operating mode and parameters.
2.  **Environment Setup**: Directories are checked/created.
3.  **Device Selection**: CUDA (GPU) is preferred over CPU.
4.  **Mode Execution**:
    *   **Train**: A `SyntheticPoseDataset` is initialized, a `SurfEmbCNN` model is created, and the `train_model` function is called to start training. If a CAD model is not found, a dummy one is created for demonstration.
    *   **Inference**: A `PoseEstimator` instance is created, and its `estimate_pose` method is called with the provided RGBD images. The estimated pose (rotation and translation vectors) is then printed, and an optional visualization is performed.

This `main.py` script provides a complete, runnable example of the SurfEmb pipeline, allowing users to quickly get started with training their own models and performing pose estimation.



## Testing Guidelines

To ensure the proper functioning of the SurfEmb algorithm, follow these testing guidelines.

### 1. Verify Environment Setup

After following the [Installation Instructions](#installation-instructions), you can verify your environment:

*   **Python Dependencies**: Run a simple Python script to import the installed libraries:

    ```bash
    python -c "import torch; import cv2; import open3d; import numpy; print('All libraries imported successfully!')"
    ```
    If any import errors occur, double-check your `pip install` commands and ensure your virtual environment is active.

*   **Intel RealSense SDK**: Verify the RealSense camera is detected and working:

    ```bash
    realsense-viewer
    ```
    This should launch the RealSense Viewer application. If not, check your SDK installation. You can also test the Python wrapper:

    ```bash
    python -c "import pyrealsense2 as rs; print('pyrealsense2 imported successfully!')"
    ```

### 2. Test Individual Modules

Each core module (`src/data_processing.py`, `src/cnn_model.py`, `src/pose_solver.py`, `src/dataset.py`, `src/training.py`, `src/inference.py`) includes an `if __name__ == "__main__":` block with example usage and assertions. You can run these modules directly to test their individual functionalities:

*   **Data Processing**: `python src/data_processing.py`
*   **CNN Model**: `python src/cnn_model.py`
*   **Pose Solver**: `python src/pose_solver.py`
*   **Synthetic Dataset**: `python src/dataset.py`
*   **Training**: `python src/training.py`
*   **Inference**: `python src/inference.py`

These tests will create dummy files and perform basic operations, printing success messages or errors. Ensure all these tests pass before proceeding.

### 3. End-to-End Training Test

To test the full training pipeline, run `main.py` in `train` mode. This will generate synthetic data, train a dummy model for a few epochs, and save it.

```bash
python main.py --mode train --num_epochs 2 --batch_size 2
```

*   **Expected Output**: You should see output indicating the progress of training, including epoch and batch loss values. A model file (e.g., `models/surfemb_model.pth`) should be created.
*   **Troubleshooting**: If training fails, check the `src/dataset.py` for issues with synthetic data generation (especially the correspondence map) or `src/cnn_model.py` for model architecture problems. Ensure your GPU is correctly configured and PyTorch can access it.

### 4. End-to-End Inference Test

Once you have a trained model (even a dummy one from the training test), you can test the inference pipeline. You will need a sample RGB and depth image. For initial testing, you can use the dummy images generated by `src/inference.py`'s `if __name__ == "__main__":` block, or create your own.

First, ensure you have dummy RGB and Depth images in your `data/` directory. You can run `python src/inference.py` once to generate them, then prevent it from cleaning up by commenting out the `os.remove` lines at the end of that script.

Then, run the main script in inference mode:

```bash
python main.py --mode inference --rgb_image data/dummy_rgb.png --depth_image data/dummy_depth.png
```

*   **Expected Output**: The script should print the estimated rotation vector (`rvec`) and translation vector (`tvec`). If visualization is enabled and your environment supports it, a window showing the projected CAD model on the RGB image should appear.
*   **Troubleshooting**: If inference fails, check if the model path is correct and if the images are accessible. If the pose is incorrect, the issue might be with the trained model (needs more training or better data), the `filter_correspondences` function, or the PnP algorithm parameters.

### 5. Real-World Data Test (Optional)

For a comprehensive test, capture actual RGBD images of your object using your Intel D435 camera and its CAD model. Replace `data/dummy_rgb.png` and `data/dummy_depth.png` with your real images and `data/object160_140.ply` with your actual CAD model. Then run the inference:

```bash
python main.py --mode inference --rgb_image path/to/your/real_rgb.png --depth_image path/to/your/real_depth.png --cad_model path/to/your/real_model.ply
```

This will give you a true indication of the algorithm's performance on your specific setup.


