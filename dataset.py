import torch
from torch.utils.data import Dataset
import numpy as np
import cv2
import os
import open3d as o3d
from scipy.spatial.transform import Rotation as R

class SyntheticPoseDataset(Dataset):
    """
    A PyTorch Dataset for generating synthetic RGBD images, object masks, and
    ground truth 3D correspondence maps for training the SurfEmbCNN.
    """
    def __init__(self, cad_model_path, camera_intrinsics, image_size=(256, 256), num_samples=1000,
                 min_distance=0.5, max_distance=1.5, fov_x=60, fov_y=45):
        """
        Initializes the synthetic dataset.

        Args:
            cad_model_path (str): Path to the object's CAD model (PLY file).
            camera_intrinsics (dict): Dictionary with camera intrinsics (fx, fy, cx, cy).
            image_size (tuple): Desired output image size (height, width).
            num_samples (int): Number of synthetic samples to generate.
            min_distance (float): Minimum distance of the object from the camera (meters).
            max_distance (float): Maximum distance of the object from the camera (meters).
            fov_x (float): Horizontal field of view of the camera in degrees.
            fov_y (float): Vertical field of view of the camera in degrees.
        """
        self.cad_model_path = cad_model_path
        self.camera_intrinsics = camera_intrinsics
        self.image_size = image_size
        self.num_samples = num_samples
        self.min_distance = min_distance
        self.max_distance = max_distance
        self.fov_x = fov_x
        self.fov_y = fov_y

        self.cad_model = self._load_and_preprocess_cad_model(cad_model_path)
        self.vertices = np.asarray(self.cad_model.vertices)
        self.faces = np.asarray(self.cad_model.triangles)

        # Create a visualizer for rendering
        self.vis = o3d.visualization.Visualizer()
        self.vis.create_window(visible=False, width=self.image_size[1], height=self.image_size[0]) # Render offscreen
        self.vis.add_geometry(self.cad_model)

        # Set camera intrinsics for Open3D renderer
        pinhole_camera_intrinsic = o3d.camera.PinholeCameraIntrinsic()
        pinhole_camera_intrinsic.set_intrinsics(
            self.image_size[1], self.image_size[0],
            self.camera_intrinsics['fx'], self.camera_intrinsics['fy'],
            self.camera_intrinsics['cx'], self.camera_intrinsics['cy']
        )
        self.vis.get_render_option().load_from_json("utils/render_option.json") # Load rendering options
        # Note: set_intrinsics method may not be available in newer Open3D versions
        # We'll set the camera parameters during rendering instead

    def _load_and_preprocess_cad_model(self, cad_model_path):
        mesh = o3d.io.read_triangle_mesh(cad_model_path)
        if not mesh.has_vertices():
            raise ValueError(f"Could not load vertices from {cad_model_path}")
        mesh.compute_vertex_normals() # Needed for realistic rendering
        return mesh

    def _generate_random_pose(self):
        # Random rotation
        rotation = R.random().as_matrix()

        # Random translation (x, y, z)
        # Z-axis: distance from camera
        z = np.random.uniform(self.min_distance, self.max_distance)
        # X, Y-axis: within camera FOV at distance z
        max_x = z * np.tan(np.deg2rad(self.fov_x / 2.0))
        max_y = z * np.tan(np.deg2rad(self.fov_y / 2.0))
        x = np.random.uniform(-max_x, max_x)
        y = np.random.uniform(-max_y, max_y)
        translation = np.array([x, y, z])

        # Create a 4x4 transformation matrix
        pose_matrix = np.eye(4)
        pose_matrix[:3, :3] = rotation
        pose_matrix[:3, 3] = translation
        return pose_matrix

    def _render_synthetic_data(self, pose_matrix):
        # Set camera extrinsic (view) matrix
        # Open3D expects camera_to_world, so invert world_to_camera (pose_matrix)
        camera_to_world = np.linalg.inv(pose_matrix)
        self.vis.get_view_control().set_lookat([0,0,0]) # Reset lookat to origin
        self.vis.get_view_control().set_up([0,-1,0]) # Reset up vector
        self.vis.get_view_control().set_front([0,0,1]) # Reset front vector
        self.vis.get_view_control().set_constant_z_far(self.max_distance + 1.0)
        self.vis.get_view_control().set_constant_z_near(self.min_distance - 0.1)
        self.vis.get_view_control().convert_from_pinhole_camera_parameters(o3d.camera.PinholeCameraParameters(), allow_arbitrary=True)
        self.vis.get_view_control().set_zoom(1.0) # Reset zoom

        # Apply the pose (world_to_camera) to the object for rendering
        # The object itself is transformed, and the camera remains static at origin looking along Z
        # This is a common way to simulate different poses
        temp_mesh = o3d.geometry.TriangleMesh(self.cad_model)
        temp_mesh.transform(pose_matrix) # Apply the pose to the object

        self.vis.clear_geometries()
        self.vis.add_geometry(temp_mesh)
        self.vis.poll_events()
        self.vis.update_renderer()

        rgb = np.asarray(self.vis.capture_screen_float_buffer(False)) * 255
        rgb = rgb.astype(np.uint8)
        depth = np.asarray(self.vis.capture_depth_float_buffer(False))

        # The depth buffer from Open3D is float in meters. Convert to uint16 mm if needed.
        depth_uint16 = (depth * 1000).astype(np.uint16)

        # Generate object mask (pixels with valid depth)
        object_mask = (depth > 0).astype(np.uint8) * 255

        # Generate ground truth correspondence map
        # This is the tricky part: for each pixel in the object_mask, we need its 3D coordinate on the CAD model.
        # Open3D's rendering doesn't directly give us this. We need to ray-cast or use vertex colors.
        # For simplicity, let's assume we can get vertex IDs or barycentric coordinates from rendering.
        # A common approach is to render vertex IDs or unique colors for each vertex/face.
        # Generate ground truth correspondence map
        correspondence_map = np.zeros((self.image_size[0], self.image_size[1], 3), dtype=np.float32)

        # A more robust way to get ground truth correspondence map for synthetic data
        # is to render the object, get the depth map, and then unproject the pixels
        # that belong to the object back into 3D space in the *object's local coordinate system*.
        # This requires knowing the camera's pose relative to the object (inverse of object's pose).

        # First, get the camera_to_world transformation from the object's world_to_camera pose_matrix
        world_to_camera = pose_matrix # This is the object's pose in camera frame
        camera_to_world = np.linalg.inv(world_to_camera)

        # Get the 3D points in camera frame from the rendered depth map
        # Iterate over pixels within the object mask
        valid_pixels = np.argwhere(object_mask > 0)
        if len(valid_pixels) > 0:
            v_coords, u_coords = valid_pixels[:, 0], valid_pixels[:, 1]
            depth_values = depth[v_coords, u_coords]

            # Unproject 2D pixels to 3D points in camera frame
            # Using the camera intrinsics and depth values
            fx, fy, cx, cy = self.camera_intrinsics['fx'], self.camera_intrinsics['fy'], \
                             self.camera_intrinsics['cx'], self.camera_intrinsics['cy']

            z_camera = depth_values # Depth is already in meters from Open3D render
            x_camera = (u_coords - cx) * z_camera / fx
            y_camera = (v_coords - cy) * z_camera / fy

            points_in_camera_frame = np.vstack([x_camera, y_camera, z_camera]).T

            # Transform these points from camera frame to object's local frame
            # This is the inverse of the object's pose (world_to_camera) applied to points in camera frame
            # R_obj_cam = world_to_camera[:3, :3].T
            # t_obj_cam = -R_obj_cam @ world_to_camera[:3, 3]
            # points_in_object_frame = (R_obj_cam @ points_in_camera_frame.T).T + t_obj_cam

            # More directly: points_in_object_frame = inverse(pose_matrix) @ [points_in_camera_frame, 1].T
            points_homog = np.hstack([points_in_camera_frame, np.ones((len(points_in_camera_frame), 1))])
            points_in_object_frame_homog = np.dot(np.linalg.inv(pose_matrix), points_homog.T).T
            points_in_object_frame = points_in_object_frame_homog[:, :3]

            # Fill the correspondence map
            for i, (v, u) in enumerate(valid_pixels):
                correspondence_map[v, u] = points_in_object_frame[i]

        return rgb, depth_uint16, object_mask, correspondence_map, pose_matrix

    def __len__(self):
        return self.num_samples

    def __getitem__(self, idx):
        pose_matrix = self._generate_random_pose()
        rgb, depth, object_mask, correspondence_map, _ = self._render_synthetic_data(pose_matrix)

        # Convert to PyTorch tensors and adjust dimensions (HWC to CHW)
        rgb_tensor = torch.from_numpy(rgb).permute(2, 0, 1).float() / 255.0
        depth_tensor = torch.from_numpy(depth).unsqueeze(0).float() / 1000.0 # Convert to meters
        object_mask_tensor = torch.from_numpy(object_mask).unsqueeze(0).float() / 255.0
        correspondence_map_tensor = torch.from_numpy(correspondence_map).permute(2, 0, 1).float()

        # Combine RGB and Depth into RGBD input (4 channels)
        rgbd_input = torch.cat([rgb_tensor, depth_tensor], dim=0)

        return rgbd_input, object_mask_tensor, correspondence_map_tensor

    def __del__(self):
        if hasattr(self, 'vis') and self.vis is not None:
            self.vis.destroy_window()

# Example usage (for testing purposes)
if __name__ == "__main__":
    # Create a dummy PLY file for testing
    if not os.path.exists("data"): os.makedirs("data")
    dummy_mesh = o3d.geometry.TriangleMesh.create_sphere(radius=0.1)
    dummy_mesh.compute_vertex_normals()
    o3d.io.write_triangle_mesh("data/dummy_model.ply", dummy_mesh)

    # Create a dummy render_option.json for Open3D
    if not os.path.exists("utils"): os.makedirs("utils")
    with open("utils/render_option.json", "w") as f:
        f.write("{\"background_color\": [0, 0, 0], \"point_size\": 1.0, \"line_width\": 1.0}")

    camera_intrinsics = {
        'fx': 615.0, 'fy': 615.0, 'cx': 320.0, 'cy': 240.0
    }
    image_size = (240, 320) # H, W

    print("Initializing SyntheticPoseDataset...")
    dataset = SyntheticPoseDataset(
        cad_model_path="data/dummy_model.ply",
        camera_intrinsics=camera_intrinsics,
        image_size=image_size,
        num_samples=5
    )

    print(f"Dataset size: {len(dataset)}")

    print("Fetching a sample...")
    rgbd_input, object_mask, correspondence_map = dataset[0]

    print(f"RGBD Input shape: {rgbd_input.shape}")        # Expected: (4, H, W)
    print(f"Object Mask shape: {object_mask.shape}")      # Expected: (1, H, W)
    print(f"Correspondence Map shape: {correspondence_map.shape}") # Expected: (3, H, W)

    assert rgbd_input.shape == (4, image_size[0], image_size[1])
    assert object_mask.shape == (1, image_size[0], image_size[1])
    assert correspondence_map.shape == (3, image_size[0], image_size[1])

    print("SyntheticPoseDataset test passed!")

    # Clean up dummy files
    os.remove("data/dummy_model.ply")
    os.remove("utils/render_option.json")
    os.rmdir("data")
    os.rmdir("utils")


