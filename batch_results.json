{"timestamp": "2025-09-23T12:25:52.147652", "model_path": "models/surfemb_model_epoch_1.pth", "total_images": 5, "results": [{"success": true, "rvec": [-0.22061548, 1.44735302, -0.97207433], "tvec": [0.08313759, 0.26608111, -0.10598619], "stdout": "Using device: cuda\nRunning in INFERENCE mode...\nUsing device for inference: cuda\nLoaded CNN model from models/surfemb_model_epoch_1.pth\nLoaded CAD model from data/object160_140.PLY\nPose estimation successful!\nRotation Vector (rvec):\n[[-0.22061548]\n [ 1.44735302]\n [-0.97207433]]\nTranslation Vector (tvec):\n[[ 0.08313759]\n [ 0.26608111]\n [-0.10598619]]\n", "stderr": "C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\torchvision\\models\\_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n  warnings.warn(\nC:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\torchvision\\models\\_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=None`.\n  warnings.warn(msg)\n", "image_name": "0000.png", "rgb_path": "data/rgb\\0000.png", "depth_path": "data/depth\\0000.png", "inference_time": 29.56279706954956}, {"success": false, "error": "Timeout after 30 seconds", "image_name": "0001.png", "rgb_path": "data/rgb\\0001.png", "depth_path": "data/depth\\0001.png", "inference_time": 30.27010440826416}, {"success": false, "error": "Timeout after 30 seconds", "image_name": "0002.png", "rgb_path": "data/rgb\\0002.png", "depth_path": "data/depth\\0002.png", "inference_time": 30.194880485534668}, {"success": false, "error": "Timeout after 30 seconds", "image_name": "0003.png", "rgb_path": "data/rgb\\0003.png", "depth_path": "data/depth\\0003.png", "inference_time": 30.313138484954834}, {"success": false, "error": "Timeout after 30 seconds", "image_name": "0004.png", "rgb_path": "data/rgb\\0004.png", "depth_path": "data/depth\\0004.png", "inference_time": 30.239614486694336}]}