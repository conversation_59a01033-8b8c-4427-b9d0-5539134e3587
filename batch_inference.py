#!/usr/bin/env python3
"""
Batch inference script for testing multiple images with the trained SurfEmb model.
This script can run while training continues in another terminal.
"""

import os
import glob
import argparse
import time
import json
from datetime import datetime
import subprocess
import sys

def run_inference_on_image(model_path, rgb_path, depth_path):
    """Run inference on a single image pair and return the results."""
    cmd = [
        sys.executable, "main.py",
        "--mode", "inference",
        "--model_output", model_path,
        "--rgb_image", rgb_path,
        "--depth_image", depth_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            # Parse the output to extract pose information
            output_lines = result.stdout.strip().split('\n')
            success = False
            rvec = None
            tvec = None
            
            for i, line in enumerate(output_lines):
                if "Pose estimation successful!" in line:
                    success = True
                elif "Rotation Vector (rvec):" in line and i+3 < len(output_lines):
                    # Extract rotation vector from next 3 lines
                    rvec = []
                    for j in range(1, 4):
                        if i+j < len(output_lines):
                            line_val = output_lines[i+j].strip()
                            if '[' in line_val and ']' in line_val:
                                val = line_val.replace('[', '').replace(']', '').strip()
                                try:
                                    rvec.append(float(val))
                                except:
                                    pass
                elif "Translation Vector (tvec):" in line and i+3 < len(output_lines):
                    # Extract translation vector from next 3 lines
                    tvec = []
                    for j in range(1, 4):
                        if i+j < len(output_lines):
                            line_val = output_lines[i+j].strip()
                            if '[' in line_val and ']' in line_val:
                                val = line_val.replace('[', '').replace(']', '').strip()
                                try:
                                    tvec.append(float(val))
                                except:
                                    pass
            
            return {
                "success": success,
                "rvec": rvec,
                "tvec": tvec,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        else:
            return {
                "success": False,
                "error": result.stderr,
                "stdout": result.stdout
            }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "error": "Timeout after 30 seconds"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def main():
    parser = argparse.ArgumentParser(description="Batch inference testing")
    parser.add_argument("--model", default="models/surfemb_model_epoch_1.pth", help="Path to trained model")
    parser.add_argument("--rgb_dir", default="data/rgb", help="Directory containing RGB images")
    parser.add_argument("--depth_dir", default="data/depth", help="Directory containing depth images")
    parser.add_argument("--max_images", type=int, default=10, help="Maximum number of images to test")
    parser.add_argument("--output", default="batch_results.json", help="Output file for results")
    
    args = parser.parse_args()
    
    # Find RGB images
    rgb_pattern = os.path.join(args.rgb_dir, "*.png")
    rgb_files = sorted(glob.glob(rgb_pattern))
    
    if not rgb_files:
        print(f"No RGB images found in {args.rgb_dir}")
        return
    
    print(f"Found {len(rgb_files)} RGB images")
    print(f"Testing with model: {args.model}")
    print(f"Testing up to {args.max_images} images...")
    print("-" * 50)
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "model_path": args.model,
        "total_images": min(len(rgb_files), args.max_images),
        "results": []
    }
    
    successful_count = 0
    
    for i, rgb_file in enumerate(rgb_files[:args.max_images]):
        # Find corresponding depth file
        base_name = os.path.basename(rgb_file)
        depth_file = os.path.join(args.depth_dir, base_name)
        
        if not os.path.exists(depth_file):
            print(f"⚠️  Skipping {base_name}: depth file not found")
            continue
        
        print(f"🔍 Testing image {i+1}/{min(len(rgb_files), args.max_images)}: {base_name}")
        
        start_time = time.time()
        result = run_inference_on_image(args.model, rgb_file, depth_file)
        end_time = time.time()
        
        result["image_name"] = base_name
        result["rgb_path"] = rgb_file
        result["depth_path"] = depth_file
        result["inference_time"] = end_time - start_time
        
        results["results"].append(result)
        
        if result["success"]:
            successful_count += 1
            print(f"   ✅ Success! Pose: R={result['rvec']}, T={result['tvec']}")
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
        
        print(f"   ⏱️  Time: {result['inference_time']:.2f}s")
        print()
    
    # Save results
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2)
    
    print("-" * 50)
    print(f"📊 SUMMARY:")
    print(f"   Total tested: {len(results['results'])}")
    print(f"   Successful: {successful_count}")
    print(f"   Failed: {len(results['results']) - successful_count}")
    print(f"   Success rate: {successful_count/len(results['results'])*100:.1f}%")
    print(f"   Results saved to: {args.output}")

if __name__ == "__main__":
    main()
