import numpy as np

# Camera Intrinsics from your dataset (info.yml)
CAMERA_INTRINSICS = {
    'fx': 611.855224609375,  # Focal length x
    'fy': 610.3872680664062,  # Focal length y
    'cx': 315.3951721191406,  # Principal point x
    'cy': 255.4595947265625   # Principal point y
}

# Camera Matrix (derived from intrinsics)
CAMERA_MATRIX = np.array([
    [CAMERA_INTRINSICS['fx'], 0, CAMERA_INTRINSICS['cx']],
    [0, CAMERA_INTRINSICS['fy'], CAMERA_INTRINSICS['cy']],
    [0, 0, 1]
], dtype=np.float32)

# Distortion Coefficients (example: no distortion)
# Replace with your camera's calibrated distortion coefficients
DIST_COEFFS = np.zeros((4, 1), dtype=np.float32)

# Model Parameters
CAD_MODEL_PATH = "data/object160_140.PLY" # Path to your object's CAD model

# Training Parameters
NUM_EPOCHS = 50
BATCH_SIZE = 10
LEARNING_RATE = 0.001
MODEL_SAVE_PATH = "models/surfemb_model.pth"

# Synthetic Dataset Parameters
SYNTHETIC_IMAGE_SIZE = (480, 640) # Height, Width - matches your dataset
SYNTHETIC_NUM_SAMPLES = 10000 # Number of synthetic samples for training
SYNTHETIC_MIN_DISTANCE = 0.5 # meters
SYNTHETIC_MAX_DISTANCE = 1.5 # meters
SYNTHETIC_FOV_X = 60 # degrees
SYNTHETIC_FOV_Y = 45 # degrees

# Inference Parameters
CONFIDENCE_THRESHOLD = 0.5 # For object detection (if used)
REPROJECTION_ERROR_PNP = 8.0 # Pixels
PNP_CONFIDENCE = 0.99
PNP_ITERATIONS = 1000

# Paths
DATA_DIR = "data"
MODELS_DIR = "models"
UTILS_DIR = "utils"

# Ensure directories exist
import os
for d in [DATA_DIR, MODELS_DIR, UTILS_DIR]:
    os.makedirs(d, exist_ok=True)


