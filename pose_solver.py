import numpy as np
import cv2

def estimate_pose_pnp_ransac(points_2d, points_3d, camera_matrix, dist_coeffs, confidence=0.99, iterations=1000, reprojection_error=8.0):
    """
    Estimates the 6D pose (rotation and translation) of an object using PnP with RANSAC.

    Args:
        points_2d (numpy.ndarray): Nx2 array of 2D image points (u, v).
        points_3d (numpy.ndarray): Nx3 array of corresponding 3D model points (x, y, z).
        camera_matrix (numpy.ndarray): 3x3 camera intrinsic matrix.
        dist_coeffs (numpy.ndarray): 1xN or Nx1 array of distortion coefficients.
        confidence (float): Confidence parameter for RANSAC. Default is 0.99.
        iterations (int): Maximum number of RANSAC iterations. Default is 1000.
        reprojection_error (float): Maximum allowed reprojection error to treat a point pair
                                   as an inlier. Default is 8.0 pixels.

    Returns:
        tuple: A tuple containing:
            - rvec (numpy.ndarray): 3x1 rotation vector (Rodrigues).
            - tvec (numpy.ndarray): 3x1 translation vector.
            - inliers (numpy.ndarray): Indices of inlier points.
            - success (bool): True if pose estimation was successful, False otherwise.
    """
    if len(points_2d) < 4 or len(points_3d) < 4:
        print("Not enough points for PnP. Need at least 4 points.")
        return None, None, None, False

    # Ensure data types are correct for OpenCV functions
    points_2d = points_2d.astype(np.float32)
    points_3d = points_3d.astype(np.float32)

    # SolvePnP with RANSAC
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        objectPoints=points_3d,
        imagePoints=points_2d,
        cameraMatrix=camera_matrix,
        distCoeffs=dist_coeffs,
        iterationsCount=iterations,
        reprojectionError=reprojection_error,
        confidence=confidence,
        flags=cv2.SOLVEPNP_EPNP # EPNP is generally a good robust choice
    )

    return rvec, tvec, inliers, success

def filter_correspondences(correspondence_map, object_mask, depth_image, camera_intrinsics, depth_scale=1000.0):
    """
    Filters valid 2D-3D correspondences from the CNN output and depth image.

    Args:
        correspondence_map (numpy.ndarray): HxWx3 array from CNN, where each pixel (x,y) contains
                                            predicted 3D (X,Y,Z) coordinates on the CAD model.
        object_mask (numpy.ndarray): HxW binary mask indicating object pixels.
        depth_image (numpy.ndarray): HxW depth image (e.g., uint16 in mm).
        camera_intrinsics (dict): Dictionary with camera intrinsics (fx, fy, cx, cy).
        depth_scale (float): Factor to convert raw depth values to meters (e.g., 1000.0 for mm to m).

    Returns:
        tuple: A tuple containing:
            - valid_points_2d (numpy.ndarray): Kx2 array of 2D image points.
            - valid_points_3d (numpy.ndarray): Kx3 array of corresponding 3D model points.
    """
    H, W, _ = correspondence_map.shape
    valid_points_2d = []
    valid_points_3d = []

    fx, fy, cx, cy = camera_intrinsics["fx"], camera_intrinsics["fy"], camera_intrinsics["cx"], camera_intrinsics["cy"]

    for v in range(H):
        for u in range(W):
            if object_mask[v, u] > 0: # If pixel belongs to the object
                # Predicted 3D point from CNN output
                predicted_3d_model_point = correspondence_map[v, u]

                # Get 3D point from depth image (in camera frame)
                raw_depth = depth_image[v, u]
                if raw_depth > 0: # Ensure valid depth reading
                    # Convert raw depth to meters
                    z_camera = raw_depth / depth_scale
                    x_camera = (u - cx) * z_camera / fx
                    y_camera = (v - cy) * z_camera / fy
                    point_3d_camera_frame = np.array([x_camera, y_camera, z_camera])

                    # For PnP, we need 2D image points and their corresponding 3D model points.
                    # The CNN directly predicts the 3D model point for the 2D image point (u,v).
                    valid_points_2d.append([u, v])
                    valid_points_3d.append(predicted_3d_model_point)

    return np.array(valid_points_2d), np.array(valid_points_3d)


# Example usage (for testing purposes)
if __name__ == "__main__":
    # Dummy data for testing
    H, W = 256, 256
    dummy_correspondence_map = np.random.rand(H, W, 3).astype(np.float32) * 10 # Random 3D points
    dummy_object_mask = np.zeros((H, W), dtype=np.uint8)
    dummy_object_mask[H//4:3*H//4, W//4:3*W//4] = 255 # A square mask
    dummy_depth_image = np.random.randint(100, 2000, size=(H, W), dtype=np.uint16) # Depth in mm
    dummy_depth_image[dummy_object_mask == 0] = 0 # No depth outside object

    dummy_camera_intrinsics = {
        "fx": 615.0, "fy": 615.0, "cx": 320.0, "cy": 240.0
    }

    # Create a dummy camera matrix and distortion coefficients
    dummy_camera_matrix = np.array([
        [dummy_camera_intrinsics["fx"], 0, dummy_camera_intrinsics["cx"]],
        [0, dummy_camera_intrinsics["fy"], dummy_camera_intrinsics["cy"]],
        [0, 0, 1]
    ], dtype=np.float32)
    dummy_dist_coeffs = np.zeros((4, 1), dtype=np.float32) # No distortion

    print("Testing correspondence filtering...")
    points_2d, points_3d = filter_correspondences(
        dummy_correspondence_map,
        dummy_object_mask,
        dummy_depth_image,
        dummy_camera_intrinsics
    )
    print(f"Filtered {len(points_2d)} 2D-3D correspondences.")
    assert len(points_2d) == np.sum(dummy_object_mask > 0), "Filtering count mismatch!"

    print("Testing PnP with RANSAC...")
    if len(points_2d) >= 4:
        rvec, tvec, inliers, success = estimate_pose_pnp_ransac(
            points_2d,
            points_3d,
            dummy_camera_matrix,
            dummy_dist_coeffs
        )

        if success:
            print("Pose estimation successful!")
            print(f"Rotation Vector (rvec):\n{rvec}")
            print(f"Translation Vector (tvec):\n{tvec}")
            print(f"Number of inliers: {len(inliers) if inliers is not None else 0}")
        else:
            print("Pose estimation failed.")
    else:
        print("Not enough valid correspondences to run PnP.")

    print("PnP+RANSAC pose solver tests completed!")


